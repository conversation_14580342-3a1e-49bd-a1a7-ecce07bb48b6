# 默认值保持功能演示

## 功能说明

脚本现在支持**智能默认值保持**功能：
- 🔄 **输入新值**: 替换为新的配置值
- ⏭️ **直接回车**: 保持原有值不变，不进行任何修改
- 📊 **详细反馈**: 清晰显示哪些配置项被更新、保持不变或跳过

## 使用演示

### 场景1: 部分更新配置

假设您只想更新服务器IP，其他配置保持不变：

```bash
=== 服务器配置 ===
服务器IP地址 [当前: *************]: **********    # 输入新值
ACP服务端口 [当前: 9100]:                          # 直接回车，保持不变

=== 系统配置 ===
忽略路径配置 [当前: /doc.html/**,/acp/app/msg/js/**]: # 直接回车，保持不变

=== 数据库配置 ===
PostgreSQL连接URL [当前: ******************************************]: # 直接回车
PostgreSQL用户名 [当前: postgres]:                  # 直接回车
PostgreSQL密码 [当前: Go@123456]: NewPassword123    # 输入新值
```

### 更新过程输出

```
[INFO] 正在更新文件: ./rs-module-acp/rs-module-acp-biz/src/main/resources/application-conf.yml
  ✓ 已更新服务器IP: ************* → **********
  - 保持ACP端口不变: 9100
  - 保持忽略路径配置不变: /doc.html/**,/acp/app/msg/js/**
  - 保持PostgreSQL连接URL不变: ******************************************
  - 保持PostgreSQL用户名不变: postgres
  ✓ 已更新PostgreSQL密码: Go@123456 → NewPassword123

[SUCCESS] 文件更新完成: ./rs-module-acp/rs-module-acp-biz/src/main/resources/application-conf.yml
  - 成功更新: 2 个配置项
  - 保持不变: 4 个配置项
```

### 场景2: 全部保持不变

如果您对所有配置都直接回车：

```bash
=== 服务器配置 ===
服务器IP地址 [当前: *************]:              # 直接回车
ACP服务端口 [当前: 9100]:                        # 直接回车

=== 系统配置 ===
忽略路径配置 [当前: /doc.html/**]:               # 直接回车
```

输出结果：
```
[INFO] 正在更新文件: ./rs-module-acp/rs-module-acp-biz/src/main/resources/application-conf.yml
  - 保持服务器IP不变: *************
  - 保持ACP端口不变: 9100
  - 保持忽略路径配置不变: /doc.html/**

[INFO] 文件无需更新: ./rs-module-acp/rs-module-acp-biz/src/main/resources/application-conf.yml
  - 保持不变: 3 个配置项
```

### 场景3: 混合更新多个文件

当有多个配置文件时，每个文件都会显示详细的更新状态：

```
[INFO] 正在更新文件: ./rs-module-acp/rs-module-acp-biz/src/main/resources/application-conf.yml
  ✓ 已更新服务器IP: ************* → **********
  - 保持ACP端口不变: 9100
  ✓ 已更新Redis主机: ************* → **********

[INFO] 正在更新文件: ./rs-module-ihc/rs-module-ihc-biz/src/main/resources/application-conf.yml
  ✓ 已更新服务器IP: ************* → **********
  ⚠ 跳过ACP端口: 在此文件中未找到该配置项
  - 保持Redis主机不变: *************
```

## 优势特性

### 1. 🎯 精确控制
- 只更新您真正想要修改的配置项
- 避免意外修改不需要变更的配置

### 2. 📋 清晰反馈
- **绿色 ✓**: 成功更新的配置项
- **蓝色 -**: 保持不变的配置项  
- **黄色 ⚠**: 跳过的配置项（文件中不存在）

### 3. 🔒 安全可靠
- 自动备份原文件
- 只修改用户明确指定的配置项
- 保持文件的其他内容完全不变

### 4. 📊 统计信息
- 显示每个文件的更新统计
- 区分更新、保持不变、跳过的配置项数量

## 实际使用建议

### 批量环境迁移
当从测试环境迁移到生产环境时：
1. 只输入需要变更的IP地址和端口
2. 密码和其他敏感配置保持不变
3. 业务相关配置保持不变

### 部分配置调优
当只需要调整特定配置时：
1. 只输入需要调优的配置项
2. 其他配置保持现有的稳定值
3. 避免引入不必要的变更风险

### 配置标准化
当需要统一某些配置时：
1. 只输入需要标准化的配置项
2. 保持各服务特有的配置不变
3. 确保配置的一致性和个性化并存

## 注意事项

1. **空值处理**: 如果当前配置项为空，直接回车不会有任何操作
2. **特殊字符**: 脚本会自动处理配置值中的特殊字符
3. **备份恢复**: 每次运行都会创建备份，可随时恢复
4. **日志记录**: 详细的操作日志帮助追踪所有变更

这种设计让配置更新变得更加灵活和安全，用户可以精确控制哪些配置需要更新，哪些需要保持不变。
