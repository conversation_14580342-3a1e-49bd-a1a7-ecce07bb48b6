package com.rs.module.acp.enums.db;

public enum ZyjwzxStatusEnum {

    DSWHCS("01", "待所务会初审"),
    DZDJDJC("02", "待诊断鉴定检查"),
    DBWJYCX("03", "待保外就医程序"),
    DHSJZD("04", "待核实居住地"),
    DSWHSY("05", "待所务会审议"),
    DSNGS("06", "待所内公示"),
    DTZSH("07", "待听证审核"),
    DZSJCSJD("08", "待驻所监察室监督"),
    DGAJGGS("09", "待公安机关公示"),
    DGAJGSP("10", "待公安机关审批"),
    DZFJFZX("11", "待罪犯交付执行"),
    YBJ("12", "已办结");

    ZyjwzxStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static ZyjwzxStatusEnum getByCode(String code) {
        for (ZyjwzxStatusEnum value : ZyjwzxStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("非法code");
    }
}
