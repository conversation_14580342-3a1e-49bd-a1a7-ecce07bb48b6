package com.rs.module.acp.enums.gj;

public enum RiskIndicatorPositiveAnomalousEnum {

    ZX("01", "正向"),
    YC("02", "异常");

    RiskIndicatorPositiveAnomalousEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;

    private String name;

    public static RiskIndicatorPositiveAnomalousEnum getByCode(String code) {
        RiskIndicatorPositiveAnomalousEnum[] enums = RiskIndicatorPositiveAnomalousEnum.values();
        for (RiskIndicatorPositiveAnomalousEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
