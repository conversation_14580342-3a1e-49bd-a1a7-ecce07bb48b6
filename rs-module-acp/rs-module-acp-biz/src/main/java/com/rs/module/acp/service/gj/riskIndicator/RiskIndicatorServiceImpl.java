package com.rs.module.acp.service.gj.riskIndicator;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.util.TemplateUtils;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.entity.OpsDicCode;
import com.rs.framework.mybatis.util.DicUtil;
import com.rs.module.acp.controller.admin.gj.vo.riskIndicator.*;
import com.rs.module.acp.enums.gj.RiskIndicatorPositiveAnomalousEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.rs.module.acp.entity.gj.RiskIndicatorDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.RiskIndicatorDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-风险指标 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RiskIndicatorServiceImpl extends BaseServiceImpl<RiskIndicatorDao, RiskIndicatorDO> implements RiskIndicatorService {

    @Resource
    private RiskIndicatorDao riskIndicatorDao;

    @Override
    public String createRiskIndicator(RiskIndicatorSaveReqVO createReqVO) {
        // 插入
        RiskIndicatorDO riskIndicator = BeanUtils.toBean(createReqVO, RiskIndicatorDO.class);
        checkRiskIndicator(riskIndicator);
        riskIndicatorDao.insert(riskIndicator);
        // 返回
        return riskIndicator.getId();
    }


    @Override
    public void updateRiskIndicator(RiskIndicatorSaveReqVO updateReqVO) {
        // 校验存在
        validateRiskIndicatorExists(updateReqVO.getId());
        // 更新
        RiskIndicatorDO updateObj = BeanUtils.toBean(updateReqVO, RiskIndicatorDO.class);
        checkRiskIndicator(updateObj);
        riskIndicatorDao.updateById(updateObj);
    }

    @Override
    public void deleteRiskIndicator(String id) {
        // 校验存在
        validateRiskIndicatorExists(id);
        // 删除
        riskIndicatorDao.deleteById(id);
    }

    private void validateRiskIndicatorExists(String id) {
        if (riskIndicatorDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-风险指标数据不存在");
        }
    }

    @Override
    public RiskIndicatorDO getRiskIndicator(String id) {
        return riskIndicatorDao.selectById(id);
    }

    @Override
    public PageResult<RiskIndicatorDO> getRiskIndicatorPage(RiskIndicatorPageReqVO pageReqVO) {
        return riskIndicatorDao.selectPage(pageReqVO);
    }

    @Override
    public List<RiskIndicatorDO> getRiskIndicatorList(RiskIndicatorListReqVO listReqVO) {
        return riskIndicatorDao.selectList(listReqVO);
    }

    @Override
    public RiskIndicatorAllInfoRespVO getDetailByJgrybm(String jgrybm) {

        String orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        RiskIndicatorListReqVO listReqVO = new RiskIndicatorListReqVO();
        listReqVO.setOrgCode(orgCode);
        listReqVO.setIsEnabled((short) 1);
        RiskIndicatorAllInfoRespVO respVO = new RiskIndicatorAllInfoRespVO();
        respVO.setBgList(new ArrayList<>());
        List<OpsDicCode> zblxList = DicUtil.getDicAsc("ZD_FXZB_ZBLX", "bsp");
        List<OpsDicCode> fxdjList = DicUtil.getDicAsc("ZD_FXZB_FXDJ", "bsp");
        Map<String, String> zblxMap = zblxList.stream().collect(Collectors.toMap(OpsDicCode::getCode, OpsDicCode::getName));
        Map<String, String> fxdjMap = fxdjList.stream().collect(Collectors.toMap(OpsDicCode::getCode, OpsDicCode::getName));
        ConcurrentMap<String, RiskIndicatorBgRespVO> riskBgMap = new ConcurrentHashMap<>();
        for (OpsDicCode opsDicCode : zblxList) {
            RiskIndicatorBgRespVO riskIndicatorBgRespVO = new RiskIndicatorBgRespVO();
            riskIndicatorBgRespVO.setIndicatorTypeCode(opsDicCode.getCode());
            riskIndicatorBgRespVO.setIndicatorTypeCodeName(zblxMap.get(riskIndicatorBgRespVO.getIndicatorTypeCode()));
            riskIndicatorBgRespVO.setIndicatorList(new CopyOnWriteArrayList<>());
            riskIndicatorBgRespVO.setRiskAtomicCount(new AtomicInteger(0));
            respVO.getBgList().add(riskIndicatorBgRespVO);
            riskBgMap.put(opsDicCode.getCode(), riskIndicatorBgRespVO);
        }
        List<RiskIndicatorDO> riskIndicatorDOS = riskIndicatorDao.selectList(listReqVO);
        if (CollectionUtil.isNotEmpty(riskIndicatorDOS)) {
            Map<String, Object> params = new HashMap<>();
            params.put("jgrybm", jgrybm);
            AtomicInteger fxIndicatorCount = new AtomicInteger(0);
            AtomicInteger jfIndicatorCount = new AtomicInteger(0);

            // 并发 计算
            for (RiskIndicatorDO riskIndicatorDO : riskIndicatorDOS) {
                String indicatorTypeCode = riskIndicatorDO.getIndicatorTypeCode();
                RiskIndicatorBgRespVO riskIndicatorBgRespVO = riskBgMap.get(indicatorTypeCode);
                if (Objects.isNull(riskIndicatorBgRespVO)) {
                    // todo countdownlatch
                    continue;
                } else {
                    riskIndicatorBgRespVO.setPositiveAnomalous(riskIndicatorDO.getPositiveAnomalous());
                    riskIndicatorBgRespVO.setPositiveAnomalousName(RiskIndicatorPositiveAnomalousEnum.getByCode(riskIndicatorDO.getPositiveAnomalous()).getName());
                    CopyOnWriteArrayList<RiskIndicatorSubBgRespVO> indicatorList = riskIndicatorBgRespVO.getIndicatorList();
                    RiskIndicatorSubBgRespVO riskIndicatorSubBgRespVO = BeanUtils.toBean(riskIndicatorDO, RiskIndicatorSubBgRespVO.class);
                    riskIndicatorSubBgRespVO.setPositiveAnomalousName(RiskIndicatorPositiveAnomalousEnum.getByCode(riskIndicatorSubBgRespVO.getPositiveAnomalous()).getName());
                    riskIndicatorSubBgRespVO.setRiskLevelName(fxdjMap.get(riskIndicatorSubBgRespVO.getRiskLevel()));
                    riskIndicatorSubBgRespVO.setIndicatorTypeCodeName(zblxMap.get(riskIndicatorSubBgRespVO.getIndicatorTypeCode()));
                    indicatorList.add(riskIndicatorSubBgRespVO);
                    // 计算 封装
                    cual(riskIndicatorDO, riskIndicatorSubBgRespVO, params, fxIndicatorCount, jfIndicatorCount, riskIndicatorBgRespVO.getRiskAtomicCount());
                }

            }
            for (RiskIndicatorBgRespVO value : riskBgMap.values()) {
                value.getIndicatorList().sort(Comparator.comparing(RiskIndicatorSubBgRespVO::getId));
            }
            respVO.setAllIndicatorCount(fxIndicatorCount.get() + jfIndicatorCount.get());
            respVO.setFxIndicatorCount(fxIndicatorCount.get());
            respVO.setJfIndicatorCount(jfIndicatorCount.get());
        }

        return respVO;
    }

    private void cual(RiskIndicatorDO riskIndicatorDO, RiskIndicatorSubBgRespVO riskIndicatorSubBgRespVO, Map<String, Object> params,
                      AtomicInteger fxIndicatorCount, AtomicInteger jfIndicatorCount, AtomicInteger riskAtomicCount) {

        String displayTemplate = riskIndicatorDO.getDisplayTemplate();
        List<JSONObject> dtList = JSON.parseArray(displayTemplate, JSONObject.class);
        Map<String, List<JSONObject>> map = new HashMap<>();
        for (JSONObject jsonObject : dtList) {
            String field = jsonObject.getString("field");
            JSONObject dic = jsonObject.getJSONObject("dic");
            if (Objects.isNull(dic)) {
                dic = new JSONObject();
            }
            if (RiskIndicatorPositiveAnomalousEnum.YC.getCode().equals(riskIndicatorDO.getPositiveAnomalous())) {
                fxIndicatorCount.addAndGet(1);
            } else {
                jfIndicatorCount.addAndGet(1);
            }
            String sql = TemplateUtils.parseTemplate(riskIndicatorDO.getQueryScript(), params, null);
            if ("one".equals(jsonObject.getString("type"))) {
                riskIndicatorSubBgRespVO.setOneDesc(jsonObject.getString("description"));
                JSONObject result = riskIndicatorDao.getOne(sql);
                if (Objects.isNull(result)) {
                    riskIndicatorSubBgRespVO.setPositiveAnomalous(null);
                    riskIndicatorSubBgRespVO.setOneResult(jsonObject.getString("default"));
                } else {
                    String condition = jsonObject.getString("condition");
                    if (StringUtils.isNotEmpty(condition)) {
                        String conditionSql = TemplateUtils.parseTemplate(condition, result, null);
                        if (!riskIndicatorDao.riskCondition(conditionSql)) {
                            riskIndicatorSubBgRespVO.setPositiveAnomalous(null);
                        }
                    }
                    riskAtomicCount.addAndGet(1);
                    oneHandle(field, result, riskIndicatorSubBgRespVO, dic);
                }

            } else {
                if (!map.containsKey("many")) {
                    List<JSONObject> many = riskIndicatorDao.getMany(sql);
                    if (CollectionUtil.isEmpty(many)) {
                        riskIndicatorSubBgRespVO.setPositiveAnomalous(null);
                        riskIndicatorSubBgRespVO.setOneResult(jsonObject.getString("default"));
                        riskIndicatorSubBgRespVO.setManyList(new ArrayList<>());
                    } else {
                        riskAtomicCount.addAndGet(1);
                    }
                    map.put("many", many);
                }
                List<JSONObject> many = map.get("many");
                if (CollectionUtil.isNotEmpty(many)) {
                    if ("many-one".equals(jsonObject.getString("type"))) {
                        riskIndicatorSubBgRespVO.setOneDesc(jsonObject.getString("description"));
                        JSONObject r1 = many.get(0);
                        r1.put("count", many.size());
                        oneHandle(field, r1, riskIndicatorSubBgRespVO, dic);
                        String condition = jsonObject.getString("condition");
                        if (StringUtils.isNotEmpty(condition)) {
                            String conditionSql = TemplateUtils.parseTemplate(condition, r1, null);
                            if (!riskIndicatorDao.riskCondition(conditionSql)) {
                                riskIndicatorSubBgRespVO.setPositiveAnomalous(null);
                            }
                        }
                    } else {
                        // many
                        if (field.contains("${")) {
                            List<String> list = new ArrayList<>();
                            for (JSONObject object : many) {
                                String[] tempManyKey = field.split("\\+");
                                for (String s : tempManyKey) {
                                    if (s.contains("${")) {
                                        String dicKey = s.replace("${", "").replace("}", "");
                                        String dicName = dic.getString(dicKey);
                                        String code = object.getString(dicKey);
                                        if (StringUtils.isNotEmpty(dicName)) {
                                            // 翻译
                                            String bspCodeName = com.bsp.common.cache.DicUtil.translate("bsp", dicName, code);
                                            object.put(dicKey, bspCodeName);
                                        }
                                    }
                                }
                                list.add(TemplateUtils.parseTemplate(field, object, null));
                            }
                            // 模板方法：
                            riskIndicatorSubBgRespVO.setManyList(list);
                        } else {
                            List<String> collect = many.stream().map(a -> a.getString(field)).collect(Collectors.toList());
                            riskIndicatorSubBgRespVO.setManyList(collect);
                        }
                    }
                }
            }
        }

    }


    private void oneHandle(String field, JSONObject result, RiskIndicatorSubBgRespVO riskIndicatorSubBgRespVO, JSONObject dic) {
        if (field.contains("${")) {
            String[] tempManyKey = field.split("\\+");
            for (String s : tempManyKey) {
                if (s.contains("${")) {
                    String dicKey = s.replace("${", "").replace("}", "");
                    String dicName = dic.getString(dicKey);
                    String code = result.getString(dicKey);
                    if (StringUtils.isNotEmpty(dicName)) {
                        // 翻译
                        String bspCodeName = com.bsp.common.cache.DicUtil.translate("bsp", dicName, code);
                        result.put(dicKey, bspCodeName);
                    }
                }
            }
            // 模板方法：
            riskIndicatorSubBgRespVO.setOneResult(TemplateUtils.parseTemplate(field, result, null));
        } else {
            riskIndicatorSubBgRespVO.setOneResult(result.getString(field));
        }
    }

    private void checkRiskIndicator(RiskIndicatorDO riskIndicator) {
        String orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        LambdaQueryWrapper<RiskIndicatorDO> wrapper = Wrappers.lambdaQuery(RiskIndicatorDO.class)
                .eq(RiskIndicatorDO::getIndicatorTypeCode, riskIndicator.getIndicatorTypeCode())
                .eq(RiskIndicatorDO::getIndicatorName, riskIndicator.getIndicatorName())
                .eq(RiskIndicatorDO::getIndicatorDescription, riskIndicator.getIndicatorDescription())
                .eq(RiskIndicatorDO::getOrgCode, orgCode);
        if (StringUtils.isNotEmpty(riskIndicator.getId())) {
            wrapper.ne(RiskIndicatorDO::getId, riskIndicator.getId());
        }
        Integer count = riskIndicatorDao.selectCount(wrapper);
        if (Objects.nonNull(count) && count > 0) {
            throw new ServerException("请勿重复配置指标");
        }
        LambdaQueryWrapper<RiskIndicatorDO> wrapper2 = Wrappers.lambdaQuery(RiskIndicatorDO.class)
                .eq(RiskIndicatorDO::getIndicatorTypeCode, riskIndicator.getIndicatorTypeCode())
                .eq(RiskIndicatorDO::getOrgCode, orgCode).last(" limit 1");
        if (StringUtils.isNotEmpty(riskIndicator.getId())) {
            wrapper2.ne(RiskIndicatorDO::getId, riskIndicator.getId());
        }
        RiskIndicatorDO riskIndicatorDO = riskIndicatorDao.selectOne(wrapper2);
        if (Objects.nonNull(riskIndicatorDO)
                && !riskIndicatorDO.getPositiveAnomalous().equals(riskIndicator.getPositiveAnomalous())) {
            throw new ServerException("同一指标类型，请保持一致的正向/异常类型");
        }

        // 校验合法性
        String queryScript = riskIndicator.getQueryScript();
        if (!isSelect(queryScript)) {
            throw new ServerException("非法查询语句");
        }
        try {
            List<JSONObject> many = riskIndicatorDao.getMany(queryScript);
        } catch (Exception e) {
            throw new ServerException("查询语句异常：" + e.getMessage());
        }

        String displayTemplate = riskIndicator.getDisplayTemplate();
        try {
            List<JSONObject> templateList = JSON.parseArray(displayTemplate, JSONObject.class);
            List<String> typeList = Arrays.asList("one,many,many-one".split(","));
            for (JSONObject jsonObject : templateList) {
                String field = jsonObject.getString("field");
                Assert.notEmpty(field, "field 不能为空");
                String type = jsonObject.getString("type");
                Assert.notEmpty(type, "type不能为空");
                if (!typeList.contains(type)) {
                    throw new ServerException("非法type");
                }
                String description = jsonObject.getString("description");
                Assert.notEmpty(description, "description不能为空");
                String condition = jsonObject.getString("condition");
                if (StringUtils.isNotEmpty(condition)) {
                    if (!isSelect(condition)) {
                        throw new ServerException("非法condition语句");
                    }
                    try {
                        riskIndicatorDao.riskCondition(condition);
                    } catch (Exception e) {
                        throw new ServerException("condition语句异常：" + e.getMessage());
                    }

                }
            }

        } catch (Exception e) {
            throw new ServerException("展示模板配置异常：" + e.getMessage());
        }


    }

    private boolean isSelect(String sql) {
        return sql.matches("^(?i)(\\s*)(select)(\\s+)(((?!(insert|delete|update)).)+)$");
    }

}
