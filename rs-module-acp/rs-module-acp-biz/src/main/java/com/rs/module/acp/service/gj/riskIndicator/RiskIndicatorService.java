package com.rs.module.acp.service.gj.riskIndicator;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.riskIndicator.RiskIndicatorAllInfoRespVO;
import com.rs.module.acp.controller.admin.gj.vo.riskIndicator.RiskIndicatorListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.riskIndicator.RiskIndicatorPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.riskIndicator.RiskIndicatorSaveReqVO;
import com.rs.module.acp.entity.gj.RiskIndicatorDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-风险指标 Service 接口
 *
 * <AUTHOR>
 */
public interface RiskIndicatorService extends IBaseService<RiskIndicatorDO>{

    /**
     * 创建实战平台-管教业务-风险指标
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createRiskIndicator(@Valid RiskIndicatorSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-风险指标
     *
     * @param updateReqVO 更新信息
     */
    void updateRiskIndicator(@Valid RiskIndicatorSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-风险指标
     *
     * @param id 编号
     */
    void deleteRiskIndicator(String id);

    /**
     * 获得实战平台-管教业务-风险指标
     *
     * @param id 编号
     * @return 实战平台-管教业务-风险指标
     */
    RiskIndicatorDO getRiskIndicator(String id);

    /**
    * 获得实战平台-管教业务-风险指标分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-风险指标分页
    */
    PageResult<RiskIndicatorDO> getRiskIndicatorPage(RiskIndicatorPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-风险指标列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-风险指标列表
    */
    List<RiskIndicatorDO> getRiskIndicatorList(RiskIndicatorListReqVO listReqVO);


    RiskIndicatorAllInfoRespVO getDetailByJgrybm(String jgrybm);
}
