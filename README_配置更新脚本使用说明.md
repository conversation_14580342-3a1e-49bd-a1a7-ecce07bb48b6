# application-conf.yml 批量配置更新脚本使用说明

## 概述

这个脚本用于批量更新项目中所有 `application-conf.yml` 配置文件。它会自动搜索当前目录及其子目录中的所有配置文件，并根据用户输入更新相应的配置项。

## 功能特性

- 🔍 **自动搜索**: 递归搜索当前目录及子目录中的所有 `application-conf.yml` 文件
- 🛡️ **安全备份**: 更新前自动备份原文件，文件名格式为 `application-conf.yml.backup.YYYYMMDD_HHMMSS`
- 🎯 **选择性更新**: 支持选择性更新配置项，跳过不需要修改的配置
- 🎨 **友好界面**: 彩色输出，清晰的操作提示和进度显示
- ✅ **批量处理**: 一次性更新所有找到的配置文件

## 支持的配置项

### 1. 服务器配置
- 服务器IP地址

### 2. 系统配置
- 忽略路径配置(ignores)

### 3. 数据库配置
- **PostgreSQL**: 服务器地址、数据库名、用户名、密码
- **MySQL**: 服务器地址、数据库名、用户名、密码

### 4. 中间件配置
- **Redis**: 服务器地址、端口、密码、数据库编号
- **MongoDB**: 连接URI
- **RabbitMQ**: 服务器地址、用户名、密码

### 5. 服务注册与发现
- **Nacos**: 端口、用户名、密码、命名空间

### 6. 文件存储配置
- 存储端点、访问密钥、秘密密钥、存储桶名称

### 7. 业务服务配置
- **BSP**: 基础URL
- **BPM**: 基础URL

### 8. 任务调度配置
- **XXL-Job**: 管理地址、管理员用户名、管理员密码、执行器IP、执行器端口

### 9. 设备配置
- **清研手环(QYSH)**: 服务器IP、WebSocket端口、Web端口、用户名、密码
- **海康威视(Haikang)**: 区域代码、组织代码、主机地址、应用密钥、应用秘钥

## 使用方法

### 在Linux/macOS环境下

1. **赋予执行权限**:
   ```bash
   chmod +x update_application_conf.sh
   ```

2. **运行脚本**:
   ```bash
   ./update_application_conf.sh
   ```

### 在Windows环境下

1. **使用Git Bash运行**:
   ```bash
   bash update_application_conf.sh
   ```

2. **或者使用WSL**:
   ```bash
   wsl bash update_application_conf.sh
   ```

## 操作流程

1. **启动脚本**: 脚本会显示欢迎信息和支持的配置项列表

2. **搜索配置文件**: 自动搜索并列出所有找到的 `application-conf.yml` 文件

3. **输入配置值**: 按照提示输入需要更新的配置值
   - **直接回车**: 保持原有值不变
   - **输入新值**: 替换为新的配置值

4. **确认更新**: 显示将要更新的文件列表，确认后开始更新

5. **执行更新**: 
   - 自动备份原文件
   - 更新配置内容
   - 显示更新进度和结果

## 配置输入示例

```
=== 服务器配置 ===
服务器IP地址 [当前: *************]: **********

=== 系统配置 ===
忽略路径配置 [当前: /doc.html/**,/acp/app/msg/js/**,/app/acp/sys/vbTask/**,/websocket/**,/acp/db/biometricInfo/cjxx/upload]: /doc.html/**,/acp/app/msg/js/**,/websocket/**

=== 数据库配置 ===
PostgreSQL服务器地址 [当前: *************:5432]: **********:5432
PostgreSQL数据库名 [当前: rs_v1]: rs_prod
PostgreSQL用户名 [当前: postgres]: admin
PostgreSQL密码 [当前: Go@123456]: NewPassword123

=== Redis配置 ===
Redis服务器地址 [当前: *************]: **********
Redis端口 [当前: 6399]: 6379
Redis密码 [当前: redisbsp]:
Redis数据库编号 [当前: 3]: 0

=== XXL-Job配置 ===
XXL-Job管理地址 [当前: http://*************:8080/xxl-job-admin]: http://**********:8080/xxl-job-admin
XXL-Job管理员用户名 [当前: admin]: admin
XXL-Job管理员密码 [当前: xxlbsp]: newpassword
XXL-Job执行器IP [当前: ]: **********
XXL-Job执行器端口 [当前: 9101]: 9102

=== 清研手环(QYSH)配置 ===
清研手环服务器IP [当前: *************]: **********
清研手环WebSocket端口 [当前: 48300]: 48300
清研手环Web端口 [当前: 8180]: 8180
清研手环用户名 [当前: admin]: admin
清研手环密码 [当前: #LocalSense]: NewPassword123

=== 海康威视(Haikang)配置 ===
海康威视区域代码 [当前: root00000000]: root11111111
海康威视组织代码 [当前: 110000113]: 110000114
海康威视主机地址 [当前: 127.0.0.1]: **********
海康威视应用密钥 [当前: xxxx]: real_app_key_123
海康威视应用秘钥 [当前: xxxx]: real_app_secret_456
```

## 安全特性

### 自动备份
- 每次更新前都会自动创建备份文件
- 备份文件命名格式: `application-conf.yml.backup.20241207_143022`
- 可以随时使用备份文件恢复原始配置

### 恢复操作
如果需要恢复到更新前的状态：
```bash
# 找到对应的备份文件
ls -la **/application-conf.yml.backup.*

# 恢复指定文件
cp path/to/application-conf.yml.backup.20241207_143022 path/to/application-conf.yml
```

## 注意事项

1. **权限要求**: 确保对目标配置文件有读写权限

2. **备份管理**: 定期清理旧的备份文件以节省磁盘空间

3. **配置验证**: 更新后建议验证配置文件的语法正确性

4. **环境隔离**: 建议在不同环境（开发、测试、生产）使用不同的配置值

5. **密码安全**: 输入密码时注意周围环境，避免密码泄露

## 故障排除

### 常见问题

1. **找不到配置文件**
   - 确认当前目录是项目根目录
   - 检查文件名是否为 `application-conf.yml`

2. **权限不足**
   ```bash
   chmod 755 update_application_conf.sh
   ```

3. **sed命令不可用**
   - 在Windows上使用Git Bash或WSL
   - 确保系统安装了必要的Unix工具

4. **备份文件过多**
   ```bash
   # 清理7天前的备份文件
   find . -name "*.backup.*" -mtime +7 -delete
   ```

## 脚本依赖

- `bash` shell
- `sed` 命令（文本替换）
- `find` 命令（文件搜索）

## 版本信息

- 脚本版本: 1.0
- 支持的配置文件格式: YAML
- 兼容性: Linux, macOS, Windows (Git Bash/WSL)

## 联系支持

如果在使用过程中遇到问题，请检查：
1. 配置文件格式是否正确
2. 是否有足够的文件权限
3. 系统是否安装了必要的命令工具

---

**提示**: 首次使用建议在测试环境中验证脚本功能，确认无误后再在生产环境使用。
