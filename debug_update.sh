#!/bin/bash

# 调试配置更新功能

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 从YAML文件中提取配置值
extract_config_value() {
    local file_path="$1"
    local key_path="$2"
    local default_value="$3"
    
    if [ ! -f "$file_path" ]; then
        echo "$default_value"
        return
    fi
    
    # 使用awk提取YAML配置值
    local value=$(awk -v key="$key_path" '
    BEGIN { 
        split(key, keys, ".")
        depth = 0
        target_depth = length(keys)
        found = 0
    }
    {
        # 计算当前行的缩进深度
        match($0, /^[ ]*/)
        current_depth = RLENGTH / 2
        
        # 移除前导空格和注释
        gsub(/^[ ]*/, "")
        gsub(/#.*$/, "")
        if ($0 == "") next
        
        # 检查是否匹配当前层级的键
        if (current_depth < target_depth && match($0, /^[^:]+:/)) {
            key_name = substr($0, 1, RSTART + RLENGTH - 2)
            gsub(/:$/, "", key_name)
            
            if (current_depth + 1 <= target_depth && key_name == keys[current_depth + 1]) {
                depth = current_depth + 1
                if (depth == target_depth) {
                    # 找到目标键，提取值
                    if (match($0, /: */)) {
                        value = substr($0, RSTART + RLENGTH)
                        gsub(/^[ ]*/, "", value)
                        gsub(/[ ]*$/, "", value)
                        if (value != "") {
                            print value
                            found = 1
                            exit
                        }
                    }
                }
            } else if (current_depth < depth) {
                depth = current_depth
            }
        }
    }' "$file_path")
    
    if [ -z "$value" ]; then
        echo "$default_value"
    else
        echo "$value"
    fi
}

# 安全转义特殊字符用于sed
escape_for_sed() {
    local input="$1"
    # 转义sed中的特殊字符
    echo "$input" | sed 's/[[\.*^$()+?{|]/\\&/g' | sed 's|/|\\/|g'
}

# 测试配置提取和更新
test_config_update() {
    local config_file="$1"
    
    if [ ! -f "$config_file" ]; then
        print_error "配置文件不存在: $config_file"
        return 1
    fi
    
    print_info "测试配置文件: $config_file"
    echo "=========================================="
    
    # 测试配置提取
    echo "1. 测试配置提取:"
    local server_ip=$(extract_config_value "$config_file" "conf.server.ip" "未找到")
    local acp_port=$(extract_config_value "$config_file" "conf.server.port.acp" "未找到")
    
    echo "  服务器IP: '$server_ip'"
    echo "  ACP端口: '$acp_port'"
    echo ""
    
    # 测试sed替换
    echo "2. 测试sed替换:"
    if [ "$server_ip" != "未找到" ] && [ ! -z "$server_ip" ]; then
        local escaped_old=$(escape_for_sed "$server_ip")
        local escaped_new=$(escape_for_sed "**********")
        
        echo "  原始IP: $server_ip"
        echo "  转义后: $escaped_old"
        echo "  新IP: **********"
        echo "  转义后: $escaped_new"
        echo ""
        
        # 创建临时文件测试
        local temp_file=$(mktemp)
        cp "$config_file" "$temp_file"
        
        echo "  执行sed命令: sed -i \"s/ip: $escaped_old/ip: $escaped_new/g\" \"$temp_file\""
        if sed -i "s/ip: $escaped_old/ip: $escaped_new/g" "$temp_file" 2>/dev/null; then
            print_success "  sed命令执行成功"
            
            # 检查是否真的更新了
            local new_ip=$(extract_config_value "$temp_file" "conf.server.ip" "未找到")
            echo "  更新后的IP: '$new_ip'"
            
            if [ "$new_ip" = "**********" ]; then
                print_success "  ✓ IP更新成功！"
            else
                print_error "  ✗ IP更新失败，实际值: $new_ip"
            fi
        else
            print_error "  sed命令执行失败"
        fi
        
        # 显示更新前后的差异
        echo ""
        echo "3. 更新前后对比:"
        echo "更新前:"
        grep -n "ip:" "$config_file" | head -3
        echo ""
        echo "更新后:"
        grep -n "ip:" "$temp_file" | head -3
        
        # 清理临时文件
        rm -f "$temp_file"
    else
        print_warning "  无法测试sed替换，因为未找到有效的IP配置"
    fi
    
    echo ""
}

# 主函数
main() {
    echo "=========================================="
    echo "  配置更新调试工具"
    echo "=========================================="
    echo ""
    
    # 查找配置文件
    local config_files=($(find . -name "application-conf.yml" -type f 2>/dev/null))
    
    if [ ${#config_files[@]} -eq 0 ]; then
        print_warning "未找到任何application-conf.yml文件"
        exit 1
    fi
    
    print_info "找到 ${#config_files[@]} 个配置文件"
    echo ""
    
    # 测试每个配置文件
    for config_file in "${config_files[@]}"; do
        test_config_update "$config_file"
        echo ""
    done
    
    print_success "调试测试完成！"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
