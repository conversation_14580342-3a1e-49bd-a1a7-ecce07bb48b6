#!/bin/bash

# 测试修复后的文件查找功能

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 查找所有application-conf.yml文件并显示信息
find_and_display_config_files() {
    print_info "正在搜索application-conf.yml文件..."
    local files=($(find . -name "application-conf.yml" -type f 2>/dev/null))
    
    if [ ${#files[@]} -eq 0 ]; then
        print_warning "未找到任何application-conf.yml文件"
        exit 1
    fi
    
    print_success "找到 ${#files[@]} 个配置文件:"
    for i in "${!files[@]}"; do
        echo "  $((i+1)). ${files[i]}"
    done
    echo ""
    
    # 将文件列表存储到全局变量
    CONFIG_FILES=("${files[@]}")
}

# 测试函数
test_file_finding() {
    echo "=========================================="
    echo "  测试文件查找功能"
    echo "=========================================="
    echo ""
    
    # 调用查找函数
    find_and_display_config_files
    
    # 显示结果
    echo "全局变量 CONFIG_FILES 内容:"
    for i in "${!CONFIG_FILES[@]}"; do
        echo "  [$i]: ${CONFIG_FILES[i]}"
    done
    echo ""
    
    echo "数组长度: ${#CONFIG_FILES[@]}"
    echo ""
    
    if [ ${#CONFIG_FILES[@]} -gt 0 ]; then
        echo "第一个文件: ${CONFIG_FILES[0]}"
        echo "最后一个文件: ${CONFIG_FILES[-1]}"
    fi
    
    echo ""
    echo "测试完成！"
}

# 主函数
main() {
    test_file_finding
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
