#!/bin/bash

# 测试菜单选择功能的简化版本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示脚本使用说明
show_usage() {
    echo "=========================================="
    echo "  application-conf.yml 批量更新工具"
    echo "=========================================="
    echo ""
    echo "此脚本将帮助您批量更新项目中所有的application-conf.yml配置文件"
    echo ""
    echo "功能特性："
    echo "• 🎯 模块化配置 - 可选择性配置特定模块"
    echo "• 🔄 智能默认值 - 直接回车保持原值不变"
    echo "• 🛡️ 自动备份 - 更新前自动备份原文件"
    echo "• 📊 详细反馈 - 清晰显示更新状态和统计"
    echo "• 🔒 安全可靠 - 只修改用户指定的配置项"
    echo ""
}

# 显示配置模块选择菜单
show_module_menu() {
    echo "=========================================="
    echo "  请选择要配置的模块"
    echo "=========================================="
    echo ""
    echo "0. 全部配置 (配置所有模块)"
    echo "1. 服务器配置 (server) - IP地址、端口"
    echo "2. 系统配置 (system) - 忽略路径"
    echo "3. 数据库配置 (database) - PostgreSQL、MySQL"
    echo "4. 中间件配置 (middleware) - Redis、MongoDB、RabbitMQ"
    echo "5. Nacos配置 (nacos) - 服务注册与发现"
    echo "6. 文件存储配置 (storage) - 对象存储"
    echo "7. 业务服务配置 (business) - BSP、BPM"
    echo "8. XXL-Job配置 (xxljob) - 任务调度"
    echo "9. 清研手环配置 (qysh) - 设备集成"
    echo "10. 海康威视配置 (haikang) - 视频监控"
    echo ""
}

# 获取用户选择的配置模块
get_module_selection() {
    SELECTED_MODULES=()
    
    while true; do
        show_module_menu
        read -p "请输入要配置的模块编号 (多个编号用空格分隔，如: 1 3 4): " selection
        
        # 如果用户直接回车，默认选择全部配置
        if [ -z "$selection" ]; then
            selection="0"
        fi
        
        # 解析用户输入
        valid_selection=true
        for num in $selection; do
            case $num in
                0)
                    SELECTED_MODULES=("server" "system" "database" "middleware" "nacos" "storage" "business" "xxljob" "qysh" "haikang")
                    break
                    ;;
                1)
                    SELECTED_MODULES+=("server")
                    ;;
                2)
                    SELECTED_MODULES+=("system")
                    ;;
                3)
                    SELECTED_MODULES+=("database")
                    ;;
                4)
                    SELECTED_MODULES+=("middleware")
                    ;;
                5)
                    SELECTED_MODULES+=("nacos")
                    ;;
                6)
                    SELECTED_MODULES+=("storage")
                    ;;
                7)
                    SELECTED_MODULES+=("business")
                    ;;
                8)
                    SELECTED_MODULES+=("xxljob")
                    ;;
                9)
                    SELECTED_MODULES+=("qysh")
                    ;;
                10)
                    SELECTED_MODULES+=("haikang")
                    ;;
                *)
                    print_error "无效的选择: $num"
                    valid_selection=false
                    break
                    ;;
            esac
        done
        
        if [ "$valid_selection" = true ] && [ ${#SELECTED_MODULES[@]} -gt 0 ]; then
            break
        else
            echo ""
            print_warning "请输入有效的模块编号！"
            echo ""
            read -p "按回车键继续..." 
            echo ""
        fi
    done
    
    # 去重
    SELECTED_MODULES=($(printf "%s\n" "${SELECTED_MODULES[@]}" | sort -u))
    
    echo ""
    if [[ " ${SELECTED_MODULES[*]} " =~ " server " ]] && [[ " ${SELECTED_MODULES[*]} " =~ " system " ]] && [[ " ${SELECTED_MODULES[*]} " =~ " database " ]] && [[ " ${SELECTED_MODULES[*]} " =~ " middleware " ]] && [[ " ${SELECTED_MODULES[*]} " =~ " nacos " ]] && [[ " ${SELECTED_MODULES[*]} " =~ " storage " ]] && [[ " ${SELECTED_MODULES[*]} " =~ " business " ]] && [[ " ${SELECTED_MODULES[*]} " =~ " xxljob " ]] && [[ " ${SELECTED_MODULES[*]} " =~ " qysh " ]] && [[ " ${SELECTED_MODULES[*]} " =~ " haikang " ]]; then
        print_info "已选择: 全部配置"
    else
        # 显示中文模块名称
        local module_names=()
        for module in "${SELECTED_MODULES[@]}"; do
            case $module in
                server) module_names+=("服务器配置") ;;
                system) module_names+=("系统配置") ;;
                database) module_names+=("数据库配置") ;;
                middleware) module_names+=("中间件配置") ;;
                nacos) module_names+=("Nacos配置") ;;
                storage) module_names+=("文件存储配置") ;;
                business) module_names+=("业务服务配置") ;;
                xxljob) module_names+=("XXL-Job配置") ;;
                qysh) module_names+=("清研手环配置") ;;
                haikang) module_names+=("海康威视配置") ;;
            esac
        done
        print_info "已选择: ${module_names[*]}"
    fi
    echo ""
}

# 检查模块是否被选中
is_module_selected() {
    local module="$1"
    for selected in "${SELECTED_MODULES[@]}"; do
        if [[ "$selected" == "$module" ]]; then
            return 0
        fi
    done
    return 1
}

# 模拟配置输入
simulate_config_input() {
    echo "请输入需要更新的配置值（直接回车跳过该项）："
    echo ""
    
    # 服务器配置
    if is_module_selected "server"; then
        echo "=== 服务器配置 ==="
        echo "服务器IP地址 [当前: *************]: (模拟输入)"
        echo "ACP服务端口 [当前: 9100]: (模拟输入)"
        echo ""
    fi
    
    # 数据库配置
    if is_module_selected "database"; then
        echo "=== 数据库配置 ==="
        echo "PostgreSQL连接URL [当前: ******************************************]: (模拟输入)"
        echo "PostgreSQL用户名 [当前: postgres]: (模拟输入)"
        echo "PostgreSQL密码 [当前: Go@123456]: (模拟输入)"
        echo ""
    fi
    
    # 中间件配置
    if is_module_selected "middleware"; then
        echo "=== 中间件配置 ==="
        echo "--- Redis配置 ---"
        echo "Redis服务器地址 [当前: *************]: (模拟输入)"
        echo "Redis端口 [当前: 6399]: (模拟输入)"
        echo ""
    fi
    
    # 其他模块类似...
    for module in "${SELECTED_MODULES[@]}"; do
        case $module in
            system)
                echo "=== 系统配置 ==="
                echo "忽略路径配置 [当前: /doc.html/**]: (模拟输入)"
                echo ""
                ;;
            nacos)
                echo "=== Nacos配置 ==="
                echo "Nacos端口 [当前: 8848]: (模拟输入)"
                echo ""
                ;;
            qysh)
                echo "=== 清研手环(QYSH)配置 ==="
                echo "清研手环服务器IP [当前: *************]: (模拟输入)"
                echo ""
                ;;
            haikang)
                echo "=== 海康威视(Haikang)配置 ==="
                echo "海康威视区域代码 [当前: root00000000]: (模拟输入)"
                echo ""
                ;;
        esac
    done
    
    print_success "配置输入完成！(这是模拟演示)"
}

# 主函数
main() {
    show_usage
    
    # 获取用户选择的配置模块
    get_module_selection
    
    # 模拟配置输入过程
    simulate_config_input
    
    print_info "测试完成！实际脚本会继续执行文件更新操作。"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
