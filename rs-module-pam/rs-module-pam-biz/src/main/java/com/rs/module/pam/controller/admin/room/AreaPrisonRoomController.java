package com.rs.module.pam.controller.admin.room;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.util.DateUtil;
import com.bsp.sdk.exception.ExceptionLogUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.entity.pm.BaseDeviceCameraDO;
import com.rs.module.acp.service.pm.BaseDeviceCameraService;
import com.rs.module.base.constant.PoliceWarderUserTypeConstant;
import com.rs.module.base.controller.admin.pm.vo.*;
import com.rs.module.base.controller.admin.pm.vo.device.BaseDeviceRespVO;
import com.rs.module.base.controller.admin.pm.vo.terminal.AreaPrisonRoomExportReqVO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.entity.pm.PrisonRoomWarderDO;
import com.rs.module.base.entity.pm.PrisonerInDO;
import com.rs.module.base.entity.pm.device.BaseDeviceDO;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import com.rs.module.base.service.pm.AreaService;
import com.rs.module.base.service.pm.PrisonRoomWarderService;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.service.pm.device.BaseDeviceService;
import com.rs.module.pam.controller.admin.room.vo.AreaPrisonRoomExportVO;
import com.rs.module.pam.entity.duty.GroupDO;
import com.rs.module.pam.entity.duty.day.DayDutyGroupDO;
import com.rs.module.pam.service.attention.AttentionPrisonerService;
import com.rs.module.pam.service.duty.GroupService;
import com.rs.module.pam.service.duty.day.DayDutyGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.map.LinkedMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "监室信息管理")
@RestController
@RequestMapping("/pam/pm/areaPrisonRoom")
@Validated
public class AreaPrisonRoomController {

    @Resource
    private AreaService areaService;
    @Resource
    private AreaPrisonRoomService areaPrisonRoomService;
    @Resource
    private PrisonerService prisonerService;
    @Resource
    private AttentionPrisonerService attentionPrisonerService;
    @Resource
    private PrisonRoomWarderService roomWarderService;
    @Resource
    private GroupService groupService;
    @Resource
    private DayDutyGroupService dayDutyGroupService;
    @Resource
    private BaseDeviceService baseDeviceService;

    //数据导入时批量处理数量
    int importBatchSize = 1000;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-监管管理-区域监室")
    @LogRecordAnnotation(bizModule = "acp:areaPrisonRoom:create", operateType = LogOperateType.CREATE, title = "创建实战平台-监管管理-区域监室",
    success = "创建实战平台-监管管理-区域监室成功", fail = "创建实战平台-监管管理-区域监室失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createAreaPrisonRoom(@Valid @RequestBody AreaPrisonRoomSaveReqVO createReqVO) {
        return success(areaPrisonRoomService.createAreaPrisonRoom(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-监管管理-区域监室")
    @LogRecordAnnotation(bizModule = "acp:areaPrisonRoom:update", operateType = LogOperateType.UPDATE, title = "更新实战平台-监管管理-区域监室",
    success = "更新实战平台-监管管理-区域监室成功", fail = "更新实战平台-监管管理-区域监室失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateAreaPrisonRoom(@Valid @RequestBody AreaPrisonRoomSaveReqVO updateReqVO) {
        areaPrisonRoomService.updateAreaPrisonRoom(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-监管管理-区域监室")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:areaPrisonRoom:delete", operateType = LogOperateType.DELETE, title = "删除实战平台-监管管理-区域监室",
    success = "删除实战平台-监管管理-区域监室成功", fail = "删除实战平台-监管管理-区域监室失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteAreaPrisonRoom(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
//           areaPrisonRoomService.deleteAreaPrisonRoom(id);
           areaService.deleteArea(id);
        }

        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-监管管理-区域监室")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编号"),
            @ApiImplicitParam(name = "roomCode", value = "监室id")
    })
    @LogRecordAnnotation(bizModule = "acp:areaPrisonRoom:get", operateType = LogOperateType.QUERY, title = "获取实战平台-监管管理-区域监室",
            bizNo = "{{#id}}", success = "获取实战平台-监管管理-区域监室成功", fail = "获取实战平台-监管管理-区域监室失败", extraInfo = "{{#id}}")
    public CommonResult<AreaPrisonRoomRespVO> getAreaPrisonRoom(@RequestParam("orgCode") String orgCode,
                                                                @RequestParam("roomCode") String roomCode) {
        AreaPrisonRoomDO areaPrisonRoom = areaPrisonRoomService.getAreaPrisonRoom(orgCode, roomCode);
        AreaPrisonRoomRespVO room = BeanUtils.toBean(areaPrisonRoom, AreaPrisonRoomRespVO.class);
        // 筛选出主管民警
//        PrisonRoomWarderDO managerWarder =
        List<PrisonRoomWarderDO> managerDOList = roomWarderService.getByRoomId(orgCode, roomCode, PoliceWarderUserTypeConstant.MANAGER);
        List<PrisonRoomWarderRespVO> managerList = new ArrayList<>();
        String managerIds = "";
        String managerSfzhs = "";
        String managerNames = "";
        if (CollectionUtil.isNotEmpty(managerDOList)) {
            PrisonRoomWarderDO managerWarder = managerDOList.get(0);
            managerList = Arrays.asList(BeanUtils.toBean(managerWarder, PrisonRoomWarderRespVO.class));
            managerIds = managerWarder.getPoliceId();
            managerSfzhs = managerWarder.getPoliceSfzh();
            managerNames = managerWarder.getPoliceName();
        }
        room.setSponsorList(managerList);
        room.setSponsorIds(managerIds);
        room.setSponsorSfzhs(managerSfzhs);
        room.setSponsorNames(managerNames);
        // 筛选出协管民警
//        PrisonRoomWarderDO assistWarder =
        List<PrisonRoomWarderDO> assistDOList = roomWarderService.getByRoomId(orgCode, roomCode, PoliceWarderUserTypeConstant.ASSIST);
        List<PrisonRoomWarderRespVO> assistList = new ArrayList<>();
        String assistIds = "";
        String assistSfzhs = "";
        String assistNames = "";
        if (CollectionUtil.isNotEmpty(assistDOList)) {
            for (PrisonRoomWarderDO assist : assistDOList) {
                PrisonRoomWarderRespVO warder = new PrisonRoomWarderRespVO();
                warder.setPoliceId(assist.getPoliceId());
                warder.setPoliceSfzh(assist.getPoliceSfzh());
                warder.setPoliceName(assist.getPoliceName());
                warder.setUserType(PoliceWarderUserTypeConstant.ASSIST);
                warder.setRoomId(roomCode);
                assistList.add(warder);

                assistIds = assistIds.equals("") ? assist.getPoliceId() : (assistIds + "," + assist.getPoliceId());
                assistSfzhs = assistSfzhs.equals("") ? assist.getPoliceSfzh() : (assistSfzhs + "," + assist.getPoliceSfzh());
                assistNames = assistNames.equals("") ? assist.getPoliceName() : (assistNames + "," + assist.getPoliceName());
             }

        }
        room.setAssistList(assistList);
        room.setAssistIds(assistIds);
        room.setAssistSfzhs(assistSfzhs);
        room.setAssistNames(assistNames);
        room.setPrisonerNum(prisonerService.getPrisonerCount(orgCode, roomCode));
        List<BaseDeviceDO> deviceList = baseDeviceService.list(new LambdaQueryWrapper<BaseDeviceDO>()
                .eq(BaseDeviceDO::getRoomId, room.getId()));
        room.setDeviceList(BeanUtils.toBean(deviceList, BaseDeviceRespVO.class));
        return success(room);
    }

    @GetMapping("/getPrisoner")
    @ApiOperation(value = "获得监室人员信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编码"),
            @ApiImplicitParam(name = "roomCode", value = "监室编码")
    })
    public CommonResult<List<PrisonerListVwRespVO>> getPrisoner(@RequestParam("orgCode") String orgCode,
                                                                @RequestParam("roomCode") String roomCode) {
        List<PrisonerInDO> list = prisonerService.getPrisonerInList(orgCode, roomCode);
        return success(BeanUtils.toBean(list, PrisonerListVwRespVO.class));
    }

    @GetMapping("/getPrisonerNum")
    @ApiOperation(value = "获得监室在押人员数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编码"),
            @ApiImplicitParam(name = "roomCode", value = "监室编码")
    })
    public CommonResult<JSONObject> getPrisonerNum(@RequestParam("orgCode") String orgCode,
                                                   @RequestParam("roomCode") String roomCode) {
        JSONObject result = new JSONObject();
        result.put("count", prisonerService.getPrisonerCount(orgCode, roomCode));
        // 今日入所
        result.put("toDayInCount", prisonerService.getPrisonerToDayInCount(orgCode, roomCode));
        // 关注人员
        result.put("attentionCount", attentionPrisonerService.getPrisonerCount(orgCode, roomCode));
        // 重病号
        result.put("diseaseCount", 0);
        // TODO
        // 重大疾病风险
        result.put("importantCount", 0);
        // 紧急风险
        result.put("emergencyCount", 0);
        return success(result);
    }

    @GetMapping("/getAdjustPrisonerNum")
    @ApiOperation(value = "获得监室人员调整数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编码"),
            @ApiImplicitParam(name = "roomCode", value = "监室编码")
    })
    public CommonResult<JSONObject> getAdjustPrisonerNum(@RequestParam("orgCode") String orgCode,
                                                      @RequestParam("roomCode") String roomCode) {
        JSONObject result = new JSONObject();
        result.put("count", 0);
        // 增加
        result.put("addCount", 0);
        // 减少
        result.put("reduceCount", 0);
        return success(result);
    }

    @GetMapping("/getOutPrisonerNum")
    @ApiOperation(value = "获得监室未归人员数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编码"),
            @ApiImplicitParam(name = "roomCode", value = "监室编码")
    })
    public CommonResult<JSONObject> getOutPrisonerNum(@RequestParam("orgCode") String orgCode,
                                                   @RequestParam("roomCode") String roomCode) {
        JSONObject result = new JSONObject();
        result.put("count", 0);
        // 所外就医
        result.put("hospitalizeCount", 0);
        // 提讯
        result.put("arraignCount", 0);
        // 家属会见
        result.put("relationCount", 0);
        // 提解
        result.put("escortCount", 0);
        // 律师会见
        result.put("attorneyCount", 0);
        return success(result);
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-监管管理-区域监室分页", hidden = true)
    @LogRecordAnnotation(bizModule = "acp:areaPrisonRoom:page", operateType = LogOperateType.QUERY, title = "获得实战平台-监管管理-区域监室分页",
    success = "获得实战平台-监管管理-区域监室分页成功", fail = "获得实战平台-监管管理-区域监室分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<AreaPrisonRoomRespVO>> getAreaPrisonRoomPage(@Valid @RequestBody AreaPrisonRoomPageReqVO pageReqVO) {
        PageResult<AreaPrisonRoomDO> pageResult = areaPrisonRoomService.getAreaPrisonRoomPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AreaPrisonRoomRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-监管管理-区域监室列表", hidden = true)
    @LogRecordAnnotation(bizModule = "acp:areaPrisonRoom:list", operateType = LogOperateType.QUERY, title = "获得实战平台-监管管理-区域监室列表",
    success = "获得实战平台-监管管理-区域监室列表成功", fail = "获得实战平台-监管管理-区域监室列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<AreaPrisonRoomRespVO>> getAreaPrisonRoomList(@Valid @RequestBody AreaPrisonRoomListReqVO listReqVO) {
    List<AreaPrisonRoomDO> list = areaPrisonRoomService.getAreaPrisonRoomList(listReqVO);
        return success(BeanUtils.toBean(list, AreaPrisonRoomRespVO.class));
    }

    @PostMapping("/importExcel")
    @ApiOperation(value = "监室信息Excel文件数据导入")
    public CommonResult<String> importExcel(@RequestParam("file") MultipartFile file, @RequestParam("orgCode") String orgCode) {
        try {
            ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
            List<Object> objects = reader.readRow(0);
            List<Map<String, Object>> userMapList;
            try {
                addHeaderAlias(reader, objects);
            } catch (Exception e) {
                return error("导入模版不正确");
            }
            try {
                // 读取数据
                userMapList = reader.readAll();
                if (CollUtil.isEmpty(userMapList)) {
                    return error("导入监室数据失败，原因：Excel表格没有数据");
                }
            } catch (Exception e) {
                // e.printStackTrace();
                return error("导入监室数据失败，原因：Excel表格没有数据");
            }
            JSONObject result = areaPrisonRoomService.validateData(userMapList, orgCode);
            List<AreaPrisonRoomDO> roomList = JSONObject.parseArray(result.getString("roomList"), AreaPrisonRoomDO.class);
            List<PrisonRoomWarderDO> warderList = JSONObject.parseArray(result.getString("warderList"), PrisonRoomWarderDO.class);
            List<AreaPrisonRoomDO> roomTemList = new ArrayList<>();
            // 插入监室数据
            for (AreaPrisonRoomDO prisonRoomDO : roomList) {
                prisonRoomDO.setIsDel(false);

                roomTemList.add(prisonRoomDO);
                // 每1000条批量执行一次
                if (roomTemList.size() >= importBatchSize) {
                    areaPrisonRoomService.replaceBatchRoom(roomTemList);
                    roomTemList.clear();
                }
            }
            // 插入剩余的监室数据
            if (!roomTemList.isEmpty()) {
                areaPrisonRoomService.replaceBatchRoom(roomTemList);
            }

            List<PrisonRoomWarderDO> warderTemList = new ArrayList<>();
            // 插入监室主协管人员数据
            for (PrisonRoomWarderDO roomWarderDO : warderList) {
                warderTemList.add(roomWarderDO);
                // 每1000条批量执行一次
                if (warderTemList.size() >= importBatchSize) {
                    areaPrisonRoomService.replaceBatchWarder(warderTemList);
                    warderTemList.clear();
                }
            }
            // 插入剩余的监室主协管人员数据
            if (!warderTemList.isEmpty()) {
                areaPrisonRoomService.replaceBatchWarder(warderTemList);
            }
        } catch (Exception e) {
            ExceptionLogUtil.handle(e);
            return error("导入监室数据失败，原因：" + e.getMessage());
        }
        return success("导入监室数据成功");
    }

    private void addHeaderAlias(ExcelReader reader, List<Object> objects) {
        reader.addHeaderAlias((String) objects.get(0), "roomName");
        reader.addHeaderAlias((String) objects.get(1), "areaId");
        reader.addHeaderAlias((String) objects.get(2), "imprisonmentAmount");
        reader.addHeaderAlias((String) objects.get(3), "statusName");
        reader.addHeaderAlias((String) objects.get(4), "roomTypeName");
        reader.addHeaderAlias((String) objects.get(5), "roomSexName");
        reader.addHeaderAlias((String) objects.get(6), "roomArea");
        reader.addHeaderAlias((String) objects.get(7), "avgBedsArea");
        reader.addHeaderAlias((String) objects.get(8), "sponsorNames");
        reader.addHeaderAlias((String) objects.get(9), "sponsorIdCards");
        reader.addHeaderAlias((String) objects.get(10), "assistNames");
        reader.addHeaderAlias((String) objects.get(11), "assistIdCards");
    }

    @PostMapping("/exportExcel")
    @ApiOperation(value = "监室信息Excel文件数据导出")
    @LogRecordAnnotation(bizModule = "acp:areaPrisonRoom:list", operateType = LogOperateType.QUERY, title = "监室信息Excel文件数据导出",
            success = "监室信息Excel文件数据导出成功", fail = "监室信息Excel文件数据导出失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<String> exportExcel(HttpServletResponse response, @RequestBody AreaPrisonRoomExportReqVO exportReqVO) {
        ServletOutputStream out = null;
        ExcelWriter writer = ExcelUtil.getWriter();
        Map<String,String> aliasMap = new LinkedMap<>();
        try {
            List<JSONObject> roomList = areaPrisonRoomService.findExportListBy(exportReqVO);
            if (!(roomList != null && roomList.size() > 0)) {
                return null;
            }
            List<AreaPrisonRoomExportVO> roomExportList =
                    JSONObject.parseArray(JSONObject.toJSONString(roomList), AreaPrisonRoomExportVO.class);
            //自定义标题别名
            writer.addHeaderAlias("roomName", "监室名称");
            writer.addHeaderAlias("areaName", "所属监区");
            writer.addHeaderAlias("imprisonmentAmount", "关押量");
            writer.addHeaderAlias("statusName", "启用状态");
            writer.addHeaderAlias("roomTypeName", "监室类型");
            writer.addHeaderAlias("roomSexName", "性别类型");
            writer.addHeaderAlias("roomArea", "监室面积(m²)");
            writer.addHeaderAlias("avgBedsArea", "人均铺位面积(m²)");
            writer.addHeaderAlias("sponsorNames", "主管民警");
            writer.addHeaderAlias("assistNames", "协管民警");

//            writer.writeRow(aliasMap, false);
            // 写出Excel
            writer.write(roomExportList, true);

            // 设置列宽
            writer.setColumnWidth(0, 20);
            writer.setColumnWidth(1, 20);
            writer.setColumnWidth(2, 10);
            writer.setColumnWidth(3, 10);
            writer.setColumnWidth(4, 10);
            writer.setColumnWidth(5, 10);
            writer.setColumnWidth(6, 15);
            writer.setColumnWidth(7, 15);
            writer.setColumnWidth(8, 30);
            writer.setColumnWidth(9, 30);

            // 调整Excel表格自适应宽度
//            writer.autoSizeColumnAll();
            // response为HttpServletResponse对象
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            // test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + DateUtil.doFormatDate("yyyyMMddHHmmss", new Date()) + ".xls");
            response.setHeader("FileName", DateUtil.doFormatDate("yyyyMMddHHmmss", new Date()) + ".xls");
            response.setHeader("Access-Control-Expose-Headers", "FileName");

            out = response.getOutputStream();
            // 推送给浏览器下载
            writer.flush(out);
        } catch (Exception e) {
            ExceptionLogUtil.handle(e);
        } finally {
            writer.close();
            IoUtil.close(out);
        }
        return null;
    }

    @GetMapping("/getRoomInfo")
    @ApiOperation(value = "获得监室人员数量")
    @LogRecordAnnotation(bizModule = "acp:areaPrisonRoom:list", operateType = LogOperateType.QUERY, title = "获得获得监室人员数量",
            success = "获得获得监室人员数量成功", fail = "获得获得监室人员数量失败", extraInfo = "{TO_JSON{#listReqVO}}")
        public CommonResult<JSONObject> getRoomPrisonerInfo(@RequestParam("orgCode") String orgCode, @RequestParam("roomId") String roomId) {
        JSONObject result = areaPrisonRoomService.getRoomPrisonerInfo(orgCode, roomId);
        List<GroupDO> groupList = groupService.selectList(orgCode, roomId);
        result.put("dutyGroupCount", groupList.size());
        List<DayDutyGroupDO> dayDutyGroupList = dayDutyGroupService.selectList(orgCode, roomId);
        result.put("dayDutyGroupCount", dayDutyGroupList.size());
        return success(result);
    }

}
