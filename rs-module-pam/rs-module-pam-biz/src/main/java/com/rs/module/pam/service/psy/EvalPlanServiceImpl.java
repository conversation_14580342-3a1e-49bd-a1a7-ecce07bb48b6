package com.rs.module.pam.service.psy;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.SnowflakeIdUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.util.BspDbUtil;
import com.rs.framework.quartz.client.HttpXxlJobApi;
import com.rs.framework.quartz.cons.ScheduleTypeEnum;
import com.rs.framework.quartz.entity.XxlJobInfo;
import com.rs.module.base.entity.pm.PrisonerInDO;
import com.rs.module.base.enums.AreaTypeEnum;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.pam.cons.CommonConstants;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanListReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanSaveReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalPushTargePageReqVO;
import com.rs.module.pam.dao.common.CommonDao;
import com.rs.module.pam.dao.psy.EvalPlanDao;
import com.rs.module.pam.dto.CronConfigDTO;
import com.rs.module.pam.dto.PushTargetDTO;
import com.rs.module.pam.entity.common.PrintDocumentDO;
import com.rs.module.pam.entity.psy.EvalPlanDO;
import com.rs.module.pam.entity.psy.EvalPlanPushRecordDO;
import com.rs.module.pam.entity.psy.EvalTableDO;
import com.rs.module.pam.enums.PsyPlanCpStatusEnum;
import com.rs.module.pam.enums.PsyTriggerTypeEnum;
import com.rs.module.pam.enums.PsyUsageStatusEnum;
import com.rs.module.pam.util.CronExpression;
import com.rs.module.pam.util.CronUtils;
import com.rs.module.pam.util.SqlParserUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 监所事务管理-心理测评计划 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class EvalPlanServiceImpl extends BaseServiceImpl<EvalPlanDao, EvalPlanDO> implements EvalPlanService {


    @Resource
    private EvalPlanDao evalPlanDao;
    @Resource
    private EvalTableService evalTableService;
    @Resource
    private BspApi bspApi;
    @Resource
    private HttpXxlJobApi httpXxlJobApi;
    @Resource
    private EvalPlanPushRecordService evalPlanPushRecordService;
    @Resource
    private PrisonerService prisonerService;
    @Resource
    private CommonDao commonDao;

    @Override
    public List<String> getLastExceTime(CronConfigDTO cronConfigDTO) throws ParseException {
        return getLastExceTime(getCron(cronConfigDTO), cronConfigDTO.getEndTime());
    }

    @Override
    public void updateEableStatus(String id, String enableStatus) {
        EvalPlanDO update = evalPlanDao.selectById(id);
        if (update == null) {
            throw new ServerException("监所事务管理-心理测评计划不存在");
        }
        // xxl-job任务状态更新
        ReturnT<String> returnT;
        if ("0".equals(enableStatus)) {
            returnT = httpXxlJobApi.stop(Integer.valueOf(update.getJobId()));
        } else {
            returnT = httpXxlJobApi.start(Integer.valueOf(update.getJobId()));
        }
        if (returnT.getCode() != 200) {
            throw new ServerException("监所事务管理-xxl-job任务状态更新失败");
        }
        try {
            EvalPlanDO evalPlanDO = new EvalPlanDO();
            evalPlanDO.setId(id);
            evalPlanDO.setEnableStatus(enableStatus);
            evalPlanDao.updateById(evalPlanDO);
        } catch (Exception e) {
            if ("0".equals(update.getEnableStatus())) {
                httpXxlJobApi.stop(Integer.valueOf(update.getJobId()));
            }
            if ("1".equals(update.getEnableStatus())) {
                httpXxlJobApi.start(Integer.valueOf(update.getJobId()));
            }
            throw new ServerException("监所事务管理-心理测评计划更新失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendPlan(String jobId, String logId) {
        EvalPlanDO evalPlanDO = evalPlanDao.selectOne(EvalPlanDO::getJobId, jobId);
        if (evalPlanDO == null) {
            throw new ServerException("监所事务管理-心理测评计划不存在");
        }
        EvalPlanDO update = new EvalPlanDO();
        update.setId(evalPlanDO.getId());
        update.setLogId(logId);
        String triggerType = evalPlanDO.getTriggerType();
        long now = new Date().getTime();
        if (PsyTriggerTypeEnum.ZQCF.getCode().equals(triggerType)) {
            CronConfigDTO configDTO = JSONUtil.toBean(evalPlanDO.getTriggerConfig(), CronConfigDTO.class);
            long startTime = configDTO.getStartTime().getTime();
            long endTime = configDTO.getEndTime().getTime();
            if (now < startTime) {
                update.setStatus(PsyPlanCpStatusEnum.WWC.getCode());
            } else if (now >= startTime && now <= endTime) {
                update.setStatus(PsyPlanCpStatusEnum.JXZ.getCode());
            } else {
                update.setStatus(PsyPlanCpStatusEnum.YSX.getCode());
            }
        } else if (PsyTriggerTypeEnum.DCCF.getCode().equals(triggerType)) {
            Date startTime = DateUtil.parse(evalPlanDO.getTriggerConfig(), "yyyy-MM-dd");
            Date endTime = DateUtils.addSeconds(startTime, 10);
            if (now < startTime.getTime()) {
                update.setStatus(PsyPlanCpStatusEnum.WWC.getCode());
            } else if (now >= startTime.getTime() && now <= endTime.getTime()) {
                update.setStatus(PsyPlanCpStatusEnum.JXZ.getCode());
            } else {
                update.setStatus(PsyPlanCpStatusEnum.YSX.getCode());
            }
        } else if (PsyTriggerTypeEnum.TJCF.getCode().equals(triggerType)) {
            update.setStatus(PsyPlanCpStatusEnum.JXZ.getCode());
        } else {
            throw new ServerException("心理测评计划-触发类型不存在");
        }
        // 生效中推送测评 , yes 强制推送
        if (update.getStatus().equals(PsyPlanCpStatusEnum.JXZ.getCode()) || "yes".equals(XxlJobHelper.getJobParam())) {
            // 获取监管人员编码
            List<Map<String, String>> jgrybms = getJgrybm(evalPlanDO);
            // 推送心理测评
            List<EvalPlanPushRecordDO> addList = pushRecord(jgrybms, evalPlanDO);
            if (addList.size() > 0) {
                evalPlanPushRecordService.saveBatch(addList);
                // 药物滥用动态检测
                ywlydtjcSync(evalPlanDO, addList);
            }
            XxlJobHelper.log("推送心理测评成功数量：", addList.size());
            // TODO 推送消息给管教
        }
        if (update.getStatus().equals(PsyPlanCpStatusEnum.JXZ.getCode()) && PsyPlanCpStatusEnum.WWC.getCode().equals(evalPlanDO.getStatus())) {
            update.setFirstExecTime(new Date());
        }
        if (update.getStatus().equals(PsyPlanCpStatusEnum.YSX.getCode())) {
            if (evalPlanDO.getJobId() != null) {
                ReturnT<String> returnT = httpXxlJobApi.stop(Integer.valueOf(evalPlanDO.getJobId()));
                if (returnT.getCode() != 200) {
                    throw new ServerException("监所事务管理-xxl-job任务状态更新失败");
                }
            }
        }
        evalPlanDao.updateById(update);
    }

    private void ywlydtjcSync(EvalPlanDO evalPlanDO, List<EvalPlanPushRecordDO> list) {
        try {
            String planCode = BspDbUtil.getParam("PLAN_CODE");
            if (StringUtils.isEmpty(planCode)) {
                planCode = "P20250721004";
            }
            if (PsyTriggerTypeEnum.TJCF.getCode().equals(evalPlanDO.getTriggerType()) && planCode.equals(evalPlanDO.getPlanCode())) {
                Set<String> jgrybms = list.stream().map(EvalPlanPushRecordDO::getJgrybm).collect(Collectors.toSet());
                LambdaQueryWrapper<EvalPlanPushRecordDO> wrapper = Wrappers.lambdaQuery(EvalPlanPushRecordDO.class)
                        .eq(EvalPlanPushRecordDO::getPlanCode, planCode)
                        .in(EvalPlanPushRecordDO::getJgrybm, jgrybms);
                List<EvalPlanPushRecordDO> tempList = evalPlanPushRecordService.list(wrapper);
                List<PrisonerInDO> userList = prisonerService.getPrisonerInOneList(jgrybms);
                Map<String, PrisonerInDO> userMap = userList.stream().collect(Collectors.toMap(PrisonerInDO::getJgrybm, Function.identity()));
                List<PrintDocumentDO> printList = new ArrayList<>();
                for (EvalPlanPushRecordDO evalPlanPushRecordDO : tempList) {
                    PrintDocumentDO printDocumentDO = BeanUtils.toBean(evalPlanPushRecordDO, PrintDocumentDO.class);
                    printDocumentDO.setId(SnowflakeIdUtil.getGuid());
                    printDocumentDO.setGlId(evalPlanPushRecordDO.getId());
                    printDocumentDO.setGlType("ywlydtjc");
                    PrisonerInDO prisonerInDO = userMap.get(evalPlanPushRecordDO.getJgrybm());
                    printDocumentDO.setWsdata(JSON.toJSONString(prisonerInDO));
                    printList.add(printDocumentDO);
                }
                commonDao.batchSavePrintDocument(printList);
            }
        } catch (Exception e) {
            log.error("药物滥用动态检测同步文书表异常：", e);
        }

    }

    @Override
    public EvalPlanDO getByPlanCode(String planCode) {
        return evalPlanDao.selectOne(EvalPlanDO::getPlanCode, planCode);
    }

    @Override
    public EvalPlanRespVO getEvalPlanRespVO(String id) {
        EvalPlanRespVO evalPlanRespVO = evalPlanDao.getEvalPlanRespVO(id);
        if (evalPlanRespVO == null) {
            throw new ServerException("监所事务管理-心理测评计划不存在");
        }
        EvalPlanDO evalPlan = getEvalPlan(id);
        BeanUtil.copyProperties(evalPlan, evalPlanRespVO);
        StringBuffer pushTargetName = new StringBuffer("");
        Set<String> areaCodes = new HashSet<>();
        Set<String> jgrybms = new HashSet<>();

        // 设置 推送对象
        if (!evalPlan.getTriggerType().equals(PsyTriggerTypeEnum.TJCF.getCode())) {
            PushTargetDTO targetDTO = JSONUtil.toBean(evalPlan.getPushTarget(), PushTargetDTO.class);
            if (targetDTO.getAreaCodes() != null && targetDTO.getAreaCodes().size() > 0) {
                List<Map<String, String>> areaList = evalPlanDao.selectAreaByCode(targetDTO.getAreaCodes());
                if (CollUtil.isNotEmpty(areaList)) {
                    String areaName = areaList.stream().map(area -> area.get("area_name")).collect(Collectors.joining("、"));
                    pushTargetName.append(areaName);
                    pushTargetName.append(" /全部在押人员");
                    for (Map<String, String> area : areaList) {
                        areaCodes.add(area.get("area_code"));
                    }
                }
            }
        }
        PushTargetDTO targetDTO = JSONUtil.toBean(evalPlan.getPushTarget(), PushTargetDTO.class);
        if (CollUtil.isNotEmpty(targetDTO.getJgrybms())) {
            jgrybms.addAll(targetDTO.getJgrybms());
        }
        if (CollUtil.isNotEmpty(targetDTO.getAreaCodes())) {
            jgrybms.addAll(targetDTO.getAreaCodes());
        }
        List<Map<String, String>> jgrybm = new ArrayList<>();
        if (CollUtil.isNotEmpty(jgrybms) || CollUtil.isNotEmpty(areaCodes)) {
            jgrybm = evalPlanDao.selectJgrybm(null, areaCodes, jgrybms, null, null);
        }
        pushTargetName.append(" / " + jgrybm.size() + "人");
        evalPlanRespVO.setPushTargetName(pushTargetName.toString());
        evalPlanRespVO.setPushTargetUserName(jgrybm.stream().map(j ->
                j.get("room_name") + "/" + j.get("xm")).collect(Collectors.joining("、")));
        return evalPlanRespVO;
    }

    @Override
    public List<Map<String, String>> getPushTargeList(EvalPushTargePageReqVO pageReqVO) {
        EvalPlanDO evalPlan = getEvalPlan(pageReqVO.getPlanId());
        if (evalPlan == null) {
            throw new ServerException("监所事务管理-心理测评计划不存在");
        }
        PushTargetDTO targetDTO = JSONUtil.toBean(evalPlan.getPushTarget(), PushTargetDTO.class);
        List<Map<String, String>> mapList = new ArrayList<>();
        if (CollUtil.isNotEmpty(targetDTO.getAreaCodes())) {
            List<Map<String, String>> resultList = evalPlanDao.selectAreaByCode(targetDTO.getAreaCodes());
            if (CollUtil.isNotEmpty(resultList)) {
                mapList.addAll(resultList);
            }
        }
        Set<String> jgrybms = targetDTO.getJgrybms();
        Set<String> jqList = new HashSet<>();
        Set<String> jsList = new HashSet<>();
        if (CollUtil.isNotEmpty(mapList)) {
            for (Map<String, String> area : mapList) {
                String areaCode = area.get("area_code");
                String areaType = area.get("area_type");
                if (AreaTypeEnum.DETENTION_AREA.getCode().equals(areaType)) {
                    jqList.add(areaCode);
                } else {
                    jsList.add(areaCode);
                }
            }
        }
        return evalPlanDao.selectJgrybm(jqList, jsList, jgrybms, pageReqVO.getXm(), pageReqVO.getAreaName());
    }

    @Override
    public boolean checkPlanByTableId(String tableId) {
        LambdaQueryWrapper<EvalPlanDO> query = Wrappers.lambdaQuery();
        query.eq(EvalPlanDO::getTableId, tableId);
        Integer count = evalPlanDao.selectCount(query);
        return count > 0;
    }

    private List<EvalPlanPushRecordDO> pushRecord(List<Map<String, String>> jgrybms, EvalPlanDO evalPlanDO) {
        List<EvalPlanPushRecordDO> addList = new ArrayList<>();
        // 推送心理测评
        for (Map<String, String> jgrybm : jgrybms) {
            String[] tableIds = evalPlanDO.getTableId().split(",");
            String[] tableNames = evalPlanDO.getTableName().split(",");
            for (int i = 0; i < tableIds.length; i++) {
                String tableId = tableIds[i];
                EvalPlanPushRecordDO recordDO = getPushRecordDO(evalPlanDO, jgrybm, tableId, tableNames[i]);
                addList.add(recordDO);
            }
        }
        return addList;
    }

    private EvalPlanPushRecordDO getPushRecordDO(EvalPlanDO evalPlanDO, Map<String, String> jgrybm, String tableId, String tableNames) {
        String evalNo = bspApi.executeByRuleCode("psy_plan_cp_no", null);
        EvalPlanPushRecordDO recordDO = new EvalPlanPushRecordDO();
        recordDO.setEvalNo(evalNo);
        recordDO.setPlanName(evalPlanDO.getPlanName());
        recordDO.setPlanType(evalPlanDO.getPlanType());
        recordDO.setPlanCode(evalPlanDO.getPlanCode());
        recordDO.setPlanAddUserName(evalPlanDO.getAddUserName());
        recordDO.setAddUser(evalPlanDO.getAddUser());
        recordDO.setAddUserName(evalPlanDO.getAddUserName());
        recordDO.setOrgName(evalPlanDO.getOrgName());
        recordDO.setOrgCode(jgrybm.get("org_code"));
        recordDO.setCityCode(evalPlanDO.getCityCode());
        recordDO.setCityName(evalPlanDO.getCityName());
        recordDO.setRegCode(evalPlanDO.getRegCode());
        recordDO.setRegName(evalPlanDO.getRegName());
        recordDO.setTableName(tableNames);
        recordDO.setTableId(tableId);
        recordDO.setJgrybm(jgrybm.get("jgrybm"));
        recordDO.setJgryxm(jgrybm.get("xm"));
        recordDO.setFillingStatus("0");
        recordDO.setScore(BigDecimal.ZERO);
        recordDO.setPushTime(new Date());
        return recordDO;
    }

    private List<Map<String, String>> getJgrybm(EvalPlanDO evalPlanDO) {
        List<Map<String, String>> resultList = new ArrayList<>(0);
        if (PsyTriggerTypeEnum.TJCF.getCode().equals(evalPlanDO.getTriggerType())) {
            // 执行sql获取监管人员编码
            List<Map<String, Object>> resultBySql = evalPlanDao.getResultBySql(evalPlanDO.getTriggerConfig());
            if (CollUtil.isNotEmpty(resultBySql)) {
                for (Map<String, Object> map : resultBySql) {
                    Map<String, String> jgrybmMap = new HashMap<>();
                    jgrybmMap.put("jgrybm", (String) map.get("jgrybm"));
                    jgrybmMap.put("xm", (String) map.get("xm"));
                    jgrybmMap.put("org_code", (String) map.get("org_code"));
                    resultList.add(jgrybmMap);
                }
            }
            return resultList;
        } else {
            PushTargetDTO targetDTO = JSONUtil.toBean(evalPlanDO.getPushTarget(), PushTargetDTO.class);
            Set<String> jgrybms = targetDTO.getJgrybms() == null ? new HashSet<>() : targetDTO.getJgrybms();
            Set<String> jqList = new HashSet<>();
            Set<String> jsList = new HashSet<>();
            if (CollUtil.isNotEmpty(targetDTO.getAreaCodes())) {
                List<Map<String, String>> mapList = evalPlanDao.selectAreaByCode(targetDTO.getAreaCodes());
                if (CollUtil.isNotEmpty(mapList)) {
                    for (Map<String, String> area : mapList) {
                        String areaCode = area.get("area_code");
                        String areaType = area.get("area_type");
                        if (AreaTypeEnum.DETENTION_AREA.getCode().equals(areaType)) {
                            jqList.add(areaCode);
                        } else {
                            jsList.add(areaCode);
                        }
                    }
                }
            }
            if (CollUtil.isEmpty(jgrybms) && CollUtil.isEmpty(jqList) && CollUtil.isEmpty(jsList)) {
                return new ArrayList<>();
            }
            List<Map<String, String>> maps = evalPlanDao.selectJgrybm(jqList, jsList, jgrybms, null, null);
            if (CollUtil.isEmpty(maps)) {
                return new ArrayList<>();
            }
            List<Map<String, String>> distinctMaps = maps.stream()
                    .collect(Collectors.toMap(
                            map -> map.get("jgrybm"),                // 使用"xm"的值作为key
                            java.util.function.Function.identity(), // 使用原始Map作为value
                            (existing, replacement) -> existing, // 冲突时保留先出现的元素
                            LinkedHashMap::new                   // 保证顺序
                    ))
                    .values()
                    .stream()
                    .collect(Collectors.toList());
            resultList.addAll(distinctMaps);
            Set<String> jgrybmsSet = new HashSet<>();
            for (Map<String, String> map : resultList) {
                jgrybmsSet.add(map.get("jgrybm"));
            }
            List<String> list = evalPlanPushRecordService.existPushRecord(evalPlanDO.getPlanCode(),
                    CollUtil.toList(evalPlanDO.getTableId().split(",")), jgrybmsSet);
            List<Map<String, String>> lastList = new ArrayList<>();
            for (Map<String, String> map : resultList) {
                if (!list.contains(map.get("jgrybm"))) {
                    lastList.add(map);
                }
            }
            resultList.clear();
            return lastList;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createOrUpdate(EvalPlanSaveReqVO createReqVO) {
        String[] tableIds = createReqVO.getTableId().split(",");
        for (String tableIdStr : tableIds) {
            EvalTableDO evalTable = evalTableService.getEvalTable(tableIdStr);
            if (evalTable == null) {
                throw new ServerException("监所事务管理-心理测评量表数据不存在");
            }
            if (!evalTable.getUsageStatus().equals(PsyUsageStatusEnum.YQY.getCode())) {
                throw new ServerException("监所事务管理-心理测评量表未启用");
            }
        }
        String tableNames = Arrays.stream(createReqVO.getTableName().split(","))
                .map(String::trim) // 去掉每个字符串前后的空白字符
                .filter(s -> !s.isEmpty()) // 过滤掉空字符串
                .collect(Collectors.joining(",\n"));
        // 处理推送对象
        String pushTarget = createReqVO.getPushTarget();
        if (StrUtil.isNotBlank(pushTarget)) {
            if (JSONUtil.isTypeJSONObject(pushTarget)) {
                PushTargetDTO targetDTO = JSONUtil.toBean(pushTarget, PushTargetDTO.class);
                createReqVO.setPushTarget(JSONUtil.toJsonStr(targetDTO));
            } else {
                throw new ServerException("心理测评-推送对象数据格式错误");
            }
        }
        createReqVO.setTableName(tableNames);
        EvalPlanDO evalPlanDO = new EvalPlanDO();
        BeanUtils.copyProperties(createReqVO, evalPlanDO);
        EvalPlanDO origin = evalPlanDao.selectById(createReqVO.getId());
        PsyTriggerTypeEnum typeEnum = PsyTriggerTypeEnum.getEnumByCode(evalPlanDO.getTriggerType());
        evalPlanDO.setPlanType(typeEnum.getPlanType());
        if (origin == null) {
            evalPlanDO.setPlanCode(getPlanCode());
            // insert
            evalPlanDao.insert(evalPlanDO);
            // 新增xxl-job任务
            createUpdateXxlJob(evalPlanDO);
            // 更新xxl-job任务ID
            EvalPlanDO update = new EvalPlanDO();
            update.setId(evalPlanDO.getId());
            update.setJobId(evalPlanDO.getJobId());
            evalPlanDao.updateById(update);
        } else {
            if (!origin.getStatus().equals(PsyPlanCpStatusEnum.WWC.getCode())) {
                throw new ServerException("监所事务管理-心理测评计划不是未开始状态");
            }
            evalPlanDO.setJobId(origin.getJobId());
            createUpdateXxlJob(evalPlanDO);
            evalPlanDO.setId(origin.getId());
            evalPlanDO.setEnableStatus(null);
            evalPlanDao.updateById(evalPlanDO);
        }
        return null;
    }

    @Override
    public void deleteEvalPlan(String id) {
        // 删除
        evalPlanDao.deleteById(id);
    }

    @Override
    public EvalPlanDO getEvalPlan(String id) {
        return evalPlanDao.selectById(id);
    }

    private void createUpdateXxlJob(EvalPlanDO evalPlanDO) {
        XxlJobInfo jobInfo = new XxlJobInfo();
        PsyTriggerTypeEnum typeEnum = PsyTriggerTypeEnum.getEnumByCode(evalPlanDO.getTriggerType());
        switch (typeEnum) {
            case ZQCF:
                CronConfigDTO configDTO = JSONUtil.toBean(evalPlanDO.getTriggerConfig(), CronConfigDTO.class);
                jobInfo.setScheduleConf(getCron(configDTO));
                break;
            case DCCF:
                Date triggerDate = DateUtil.parse(evalPlanDO.getTriggerConfig(), "yyyy-MM-dd");
                jobInfo.setScheduleConf(CronUtils.generateOneTimeCron(triggerDate));
                break;
            case TJCF:
                String triggerConfig = evalPlanDO.getTriggerConfig();
                if (!SqlParserUtil.isSafeQuery(triggerConfig)) {
                    throw new ServerException("监所事务管理-触发脚本存在安全风险");
                }
                try {
                    List<String> fields = SqlParserUtil.parseSqlSelectFields(triggerConfig);
                    if (!fields.contains("jgrybm") || !fields.contains("xm") || !fields.contains("org_code")) {
                        throw new ServerException("触发脚本中必须包含jgrybm,xm,org_code字段查询结果");
                    }
                } catch (Exception e) {
                    throw new ServerException("触发脚本中必须包含jgrybm,xm,org_code字段查询结果");
                }
                // 每5分钟执行一次
                jobInfo.setScheduleConf("0 0/5 * * * ?");
                evalPlanDO.setStatus(PsyPlanCpStatusEnum.JXZ.getCode());
                break;
            default:
                throw new ServerException("监所事务管理-心理测评计划触发类型错误");
        }
        if (StrUtil.isNotBlank(evalPlanDO.getJobId())) {
            jobInfo.setId(Integer.valueOf(evalPlanDO.getJobId()));
        }
        Map<String, Object> groupPageList = httpXxlJobApi.jobGroupPageList(0, 10, "pam-server", null);
        if (groupPageList == null) {
            throw new ServerException("获取xxl-job执行器列表失败");
        }
        Object data = groupPageList.get("data");
        List<Map> list = JSONUtil.toList(data.toString(), Map.class);
        if (list.isEmpty()) {
            throw new ServerException("未获取xxl-job监室事务的执行器列表");
        }
        int groupId = (Integer) list.get(0).get("id");
        jobInfo.setJobDesc("管教-心理测评-" + evalPlanDO.getPlanName());
        jobInfo.setJobGroup(groupId);
        jobInfo.setAuthor(SessionUserUtil.getSessionUser().getName());
        jobInfo.setTriggerStatus(Integer.valueOf(evalPlanDO.getEnableStatus()));
        //运行策略类型
        jobInfo.setScheduleType(ScheduleTypeEnum.CRON.getXxlValue());
        jobInfo.setMisfireStrategy("DO_NOTHING");
        jobInfo.setExecutorRouteStrategy("RANDOM");    //随机执行器执行
        jobInfo.setExecutorHandler(CommonConstants.PSY_EXECTOR_JOBHANDLER);
        jobInfo.setExecutorParam("");
        jobInfo.setExecutorBlockStrategy("SERIAL_EXECUTION");    //单机串行
        jobInfo.setGlueType("BEAN");
        // 添加xxl-job定时任务
        ReturnT<String> returnT;
        if (jobInfo.getId() == 0) {
            returnT = httpXxlJobApi.add(jobInfo);
        } else {
            returnT = httpXxlJobApi.update(jobInfo);
        }
        if (returnT.getCode() != 200) {
            throw new ServerException("xxl-job定时任务添加or更新失败：" + returnT.getMsg());
        }
        evalPlanDO.setJobId(returnT.getContent());
    }

    @Override
    public List<EvalPlanDO> getEvalPlanList(EvalPlanListReqVO listReqVO) {
        return evalPlanDao.selectList(listReqVO);
    }

    /**
     * 根据cronConfigDTO生成cron表达式
     *
     * @param cronConfigDTO
     * @return
     */
    private String getCron(CronConfigDTO cronConfigDTO) {
        String cron;
        switch (cronConfigDTO.getType()) {
            case "day":
                cron = CronUtils.generateDailyCron(cronConfigDTO.getStartTime(), cronConfigDTO.getEndTime(),
                        Integer.valueOf(cronConfigDTO.getNum()));
                break;
            case "week":
                cron = CronUtils.generateWeeklyCron(cronConfigDTO.getNum(), cronConfigDTO.getStartTime(),
                        cronConfigDTO.getEndTime());
                break;
            case "month":
                cron = CronUtils.generateMonthlyCron(Integer.valueOf(cronConfigDTO.getNum()),
                        cronConfigDTO.getStartTime(), cronConfigDTO.getEndTime());
                break;
            case "year":
                cron = CronUtils.generateYearCron(DateUtil.parse(cronConfigDTO.getNum(), "yyyy-MM-dd"),
                        cronConfigDTO.getStartTime(), cronConfigDTO.getEndTime());
                break;
            default:
                cron = cronConfigDTO.getCron();
        }
        return cron;
    }

    private List<String> getLastExceTime(String cron, Date endTime) throws ParseException {
        List<String> result = new ArrayList<>();
        Date fromTime = new Date();
        for (int i = 0; i < 5; i++) {
            fromTime = new CronExpression(cron).getNextValidTimeAfter(fromTime);
            if (endTime != null) {
                if (!fromTime.after(endTime)) {
                    result.add(DateUtil.formatDateTime(fromTime));
                }
            } else {
                result.add(DateUtil.formatDateTime(fromTime));
            }
        }
        return result;
    }

    private String getPlanCode() {
        return bspApi.executeByRuleCode("psy_plan_no", null);
    }


}
