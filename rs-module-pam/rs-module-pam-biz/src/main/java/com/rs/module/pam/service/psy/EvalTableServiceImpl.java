package com.rs.module.pam.service.psy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableListReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableOptionRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTablePageReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTablePrewCommonAnswerRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTablePrewCommonRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTablePrewSimpleAnswerRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTablePrewSimpleRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableQuestionListReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableQuestionRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableSaveReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableSubmitReqVO;
import com.rs.module.pam.dao.psy.EvalTableDao;
import com.rs.module.pam.dto.AnswerCardDTO;
import com.rs.module.pam.dto.QuestionDTO;
import com.rs.module.pam.dto.ResultInterpRuleDTO;
import com.rs.module.pam.entity.psy.EvalPlanPushRecordAnswerDO;
import com.rs.module.pam.entity.psy.EvalTableDO;
import com.rs.module.pam.entity.psy.EvalTableQuestionDO;
import com.rs.module.pam.enums.PsyQuestionTypeEnum;
import com.rs.module.pam.enums.PsyScoreRuleEnum;
import com.rs.module.pam.enums.PsyUsageStatusEnum;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 监所事务管理-心理测评量表管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EvalTableServiceImpl extends BaseServiceImpl<EvalTableDao, EvalTableDO> implements EvalTableService {

    @Resource
    private EvalTableDao evalTableDao;
    @Resource
    private BspApi bspApi;
    @Resource
    @Lazy
    private EvalTableQuestionService evalTableQuestionService;
    @Resource
    private EvalTableOptionService evalTableOptionService;
    @Resource
    @Lazy
    private EvalPlanPushRecordAnswerService evalPlanPushRecordAnswerService;
    @Resource
    @Lazy
    private EvalPlanService evalPlanService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createEvalTable(EvalTableSaveReqVO createReqVO) {
        // 插入
        String psyNo = bspApi.executeByRuleCode("psy_no", null);
        EvalTableDO evalTable = BeanUtils.toBean(createReqVO, EvalTableDO.class);
        evalTable.setTableNo(psyNo);
        evalTable.setUsageStatus(PsyUsageStatusEnum.WWC.getCode());
        evalTableDao.insert(evalTable);
        // 返回
        return evalTable.getId();
    }

    @Override
    public void updateEvalTable(EvalTableSaveReqVO updateReqVO) {
        // 校验存在
        validateEvalTableExists(updateReqVO.getId());
        if ("5".equals(updateReqVO.getTableType()) && PsyUsageStatusEnum.YQY.getCode().equals(updateReqVO.getUsageStatus())) {
            throw new ServerException("已启用的药物滥用动态监测调查表类型,不能修改");
        }
        // 更新
        EvalTableDO updateObj = BeanUtils.toBean(updateReqVO, EvalTableDO.class);
        // 插入题目
        evalTableDao.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteEvalTable(String id) {
        // 校验存在
        EvalTableDO evalTableDO = evalTableDao.selectById(id);
        if (evalTableDO == null) {
            return;
        }
        if (!PsyUsageStatusEnum.WWC.getCode().equals(evalTableDO.getUsageStatus())) {
            if (evalPlanService.checkPlanByTableId(id)) {
                throw new ServerException("该量表已关联心理测试，不能删除");
            }
        }
        // 删除
        evalTableDao.deleteById(id);
        // 删除题目
        evalTableDao.deleteQuestion(id);
    }

    private void validateEvalTableExists(String id) {
        if (evalTableDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-心理测评量表管理数据不存在");
        }
    }

    @Override
    public EvalTableDO getEvalTable(String id) {
        return evalTableDao.selectById(id);
    }

    @Override
    public PageResult<EvalTableDO> getEvalTablePage(EvalTablePageReqVO pageReqVO) {
        return evalTableDao.selectPage(pageReqVO);
    }

    @Override
    public List<EvalTableDO> getEvalTableList(EvalTableListReqVO listReqVO) {
        return evalTableDao.selectList(listReqVO);
    }

    @Override
    public void updateTablTtotalQuestionNumber(String id, Integer totalQuestionNumber) {
        LambdaUpdateWrapper<EvalTableDO> update = Wrappers.lambdaUpdate(EvalTableDO.class);
        update.set(EvalTableDO::getTotalQuestionNumber, totalQuestionNumber)
                .eq(EvalTableDO::getId, id);
        evalTableDao.update(null, update);
    }

    @Override
    public void updateUsageStatus(String id, String usageStatus) {
        EvalTableDO evalTableDO = evalTableDao.selectById(id);
        if (evalTableDO == null) {
            throw new ServerException("心理测评量表管理数据不存在");
        }
        if (PsyUsageStatusEnum.WWC.getCode().equals(usageStatus)) {
            throw new ServerException("心理测评量表使用状态不能为未完成");
        }
        EvalTableDO updateObj = new EvalTableDO();
        updateObj.setId(id);
        updateObj.setUsageStatus(usageStatus);
        evalTableDao.updateById(updateObj);
    }

    @Override
    public void submitEvalTable(EvalTableSubmitReqVO submitReqVO) {
        EvalTableDO evalTableDO = evalTableDao.selectById(submitReqVO.getId());
        if (evalTableDO == null) {
            throw new ServerException("心理测评量表管理数据不存在");
        }
        if (evalTableDO.getTotalQuestionNumber() == 0) {
            throw new ServerException("请添加题目");
        }
        EvalTableDO updateObj = new EvalTableDO();
        if (!"3".equals(evalTableDO.getScoreRule())) {
            List<EvalTableSubmitReqVO.ResultRule> resultInterpRules = submitReqVO.getResultInterpRules();
            if (CollUtil.isNotEmpty(resultInterpRules)) {
                for (EvalTableSubmitReqVO.ResultRule resultRule : resultInterpRules) {
                    if (resultRule.getResult() == null || resultRule.getResult().trim().isEmpty()) {
                        throw new ServerException("结果解释不能为空");
                    }
                    if (resultRule.getMin() > resultRule.getMax()) {
                        throw new ServerException("结果解释范围错误");
                    }
                }
                updateObj.setResultInterpRule(JSONUtil.toJsonStr(resultInterpRules));
            }
        }
        updateObj.setId(submitReqVO.getId());
        updateObj.setScoreRule(submitReqVO.getScoreRule());
        updateObj.setCustomScoreRule(submitReqVO.getCustomScoreRule());
        updateObj.setUsageStatus(PsyUsageStatusEnum.YQY.getCode());
        evalTableDao.updateById(updateObj);
    }

    @Override
    public EvalTablePrewCommonRespVO tablePreviewCommon(String id) {
        EvalTableDO evalTableDO = evalTableDao.selectById(id);
        if (evalTableDO == null) {
            throw new ServerException("心理测评量表数据不存在");
        }
        EvalTablePrewCommonRespVO evalTablePrewRespVO = new EvalTablePrewCommonRespVO();
        BeanUtils.copyProperties(evalTableDO, evalTablePrewRespVO);
        // 题目列表
        EvalTableQuestionListReqVO listReqVO = new EvalTableQuestionListReqVO();
        listReqVO.setTableId(id);
        List<EvalTableQuestionRespVO> questionRespVOS = evalTableQuestionService.getEvalTableQuestionListRespVO(listReqVO);
        if (CollUtil.isNotEmpty(questionRespVOS)) {
            Map<String, List<EvalTableQuestionRespVO>> listMap = questionRespVOS.stream()
                    .collect(Collectors.groupingBy(EvalTableQuestionRespVO::getQuestionType));
            listMap.forEach((key, value) -> {
                QuestionDTO questionDTO = new QuestionDTO();
                questionDTO.setTotal(value.size());
                List<QuestionDTO.NumItem> numItems = new LinkedList<>();
                List<QuestionDTO.Question> questions = new LinkedList<>();
                for (int i = 0; i < value.size(); i++) {
                    EvalTableQuestionRespVO questionRespVO = value.get(i);
                    QuestionDTO.NumItem numItem = new QuestionDTO.NumItem();
                    numItem.setTh(questionRespVO.getSortOrder());
                    numItem.setQuestionId(questionRespVO.getId());
                    numItems.add(numItem);
                    QuestionDTO.Question question = new QuestionDTO.Question();
                    question.setTh(questionRespVO.getSortOrder());
                    question.setQuestionId(questionRespVO.getId());
                    question.setQuestionText(questionRespVO.getQuestionText());
                    question.setAnswer(questionRespVO.getAnswer());
                    question.setScore(questionRespVO.getScore());
                    List<QuestionDTO.Option> options = new LinkedList<>();
                    List<EvalTableOptionRespVO> optionsRespVOS = questionRespVO.getOptions();
                    if (CollUtil.isNotEmpty(optionsRespVOS)) {
                        for (int j = 0; j < optionsRespVOS.size(); j++) {
                            QuestionDTO.Option option = new QuestionDTO.Option();
                            option.setOptionCode(optionsRespVOS.get(j).getOptionCode());
                            option.setOptionText(optionsRespVOS.get(j).getOptionText());
                            option.setQuestionId(questionRespVO.getId());
                            option.setScore(optionsRespVOS.get(j).getScore());
                            options.add(option);
                        }
                    }
                    question.setOptions(options);
                    questions.add(question);
                }
                questionDTO.setNumItems(numItems);
                questionDTO.setQuestions(questions);
                if (key.equals(PsyQuestionTypeEnum.Radio.getCode())) {
                    evalTablePrewRespVO.setDxt(questionDTO);
                } else if (key.equals(PsyQuestionTypeEnum.Multi.getCode())) {
                    evalTablePrewRespVO.setFxt(questionDTO);
                } else if (key.equals(PsyQuestionTypeEnum.Answer.getCode())) {
                    evalTablePrewRespVO.setJdt(questionDTO);
                } else {
                    throw new ServerException("题目类型错误");
                }
            });
        }
        return evalTablePrewRespVO;
    }

    @Override
    public EvalTablePrewSimpleRespVO tablePreviewSimple(String id, Integer sortOrder) {
        if (sortOrder < 1) {
            throw new ServerException("排序序号必须大于0");
        }
        EvalTableDO evalTableDO = evalTableDao.selectById(id);
        if (evalTableDO == null) {
            throw new ServerException("心理测评量表数据不存在");
        }
        LambdaQueryWrapper<EvalTableQuestionDO> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(EvalTableQuestionDO::getTableId, id)
                .orderByAsc(EvalTableQuestionDO::getSortOrder);
        List<EvalTableQuestionDO> evalTableQuestionList = evalTableQuestionService.list(lambdaQuery);
        if (CollUtil.isEmpty(evalTableQuestionList)) {
            return null;
        }
        if (sortOrder > evalTableQuestionList.size()) {
            return null;
        }
        EvalTablePrewSimpleRespVO respVO = new EvalTablePrewSimpleRespVO();
        respVO.setTotal(evalTableQuestionList.size());
        for (int i = 0; i < evalTableQuestionList.size(); i++) {
            EvalTableQuestionDO questionDO = evalTableQuestionList.get(i);
            if (sortOrder == questionDO.getSortOrder()) {
                respVO.setQuestionType(questionDO.getQuestionType());
                respVO.setQuestionText(questionDO.getQuestionText());
                respVO.setAnswer(questionDO.getAnswer());
                respVO.setTh(sortOrder);
                respVO.setScore(questionDO.getScore());
                respVO.setId(questionDO.getId());
                respVO.setTableId(id);
                respVO.setScoreRule(questionDO.getScoreRule());
                List<EvalTableOptionRespVO> options = evalTableOptionService.getListByQuestionId(questionDO.getId());
                if (CollUtil.isNotEmpty(options)) {
                    respVO.setOptions(options);
                }
                int count = evalTableQuestionService.count(Wrappers.<EvalTableQuestionDO>lambdaQuery()
                        .eq(EvalTableQuestionDO::getQuestionType, questionDO.getQuestionType())
                        .eq(EvalTableQuestionDO::getTableId, id));
                respVO.setTotalType(count);
                break;
            }
        }
        return respVO;
    }

    @Override
    public List<EvalTablePrewSimpleRespVO> tablePreviewSimple(String id) {
        EvalTableDO evalTableDO = evalTableDao.selectById(id);
        if (evalTableDO == null) {
            throw new ServerException("心理测评量表数据不存在");
        }
        LambdaQueryWrapper<EvalTableQuestionDO> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(EvalTableQuestionDO::getTableId, id).orderByAsc(EvalTableQuestionDO::getSortOrder);
        List<EvalTableQuestionDO> evalTableQuestionList = evalTableQuestionService.list(lambdaQuery);
        List<EvalTablePrewSimpleRespVO> result = new LinkedList<>();
        if (CollUtil.isNotEmpty(evalTableQuestionList)) {
            Map<String, Long> countMap = evalTableQuestionList.stream()
                    .collect(Collectors.groupingBy(e -> String.valueOf(e.getQuestionType()),  // 强制转为String
                            Collectors.counting()));
            for (int i = 0; i < evalTableQuestionList.size(); i++) {
                EvalTablePrewSimpleRespVO respVO = new EvalTablePrewSimpleRespVO();
                respVO.setTotal(evalTableQuestionList.size());
                EvalTableQuestionDO questionDO = evalTableQuestionList.get(i);
                respVO.setQuestionType(questionDO.getQuestionType());
                respVO.setQuestionText(questionDO.getQuestionText());
                respVO.setAnswer(questionDO.getAnswer());
                respVO.setTh(questionDO.getSortOrder());
                respVO.setScore(questionDO.getScore());
                respVO.setId(questionDO.getId());
                respVO.setTableId(id);
                respVO.setScoreRule(questionDO.getScoreRule());
                List<EvalTableOptionRespVO> options = evalTableOptionService.getListByQuestionId(questionDO.getId());
                if (CollUtil.isNotEmpty(options)) {
                    respVO.setOptions(options);
                }
                Long aLong = countMap.get(questionDO.getQuestionType());
                if (aLong != null) {
                    respVO.setTotalType(aLong.intValue());
                } else {
                    respVO.setTotalType(0);
                }
                result.add(respVO);
            }
        }
        return result;
    }

    @Override
    public void judgeQuestion(AnswerCardDTO answerCardDTO) {
        EvalTableDO evalTableDO = evalTableDao.selectById(answerCardDTO.getTableId());
        if (evalTableDO == null) {
            throw new ServerException("心理测评量表数据不存在");
        }
        Map<String, EvalTableQuestionRespVO> questionMap = evalTableQuestionService
                .getEvalTableQuestionMap(evalTableDO.getId());
        // 判题
        List<AnswerCardDTO.AnswerCardItemDTO> items = answerCardDTO.getItems();
        for (AnswerCardDTO.AnswerCardItemDTO item : items) {
            String questionId = item.getQuestionId();
            EvalTableQuestionRespVO questionRespVO = questionMap.get(questionId);
            if (questionRespVO == null) {
                throw new ServerException("题目不存在");
            }
            Map<String, EvalTableOptionRespVO> optionMap = questionRespVO.getOptions().
                    stream().collect(Collectors.toMap(option -> option.getOptionCode(), option -> option,
                            (a, b) -> a));
            AtomicInteger score = new AtomicInteger(0);
            if (PsyScoreRuleEnum.XXJDF.getCode().equals(questionRespVO.getScoreRule())) {
                // 选项即得分
                if (PsyQuestionTypeEnum.Radio.getCode().equals(questionRespVO.getQuestionType())
                        || PsyQuestionTypeEnum.Multi.getCode().equals(questionRespVO.getQuestionType())) {
                    String[] anwserOptionCodes = item.getAnswerOptionCode().split(",");
                    for (String optionCode : anwserOptionCodes) {
                        score.addAndGet(getScore(optionCode, optionMap));
                    }
                } else if (PsyQuestionTypeEnum.Answer.getCode().equals(questionRespVO.getQuestionType())) {
                    // 简答题
                    if (StrUtil.isNotBlank(item.getAnswerOptionCode())) {
                        score.set(questionRespVO.getScore().intValue());
                    }
                } else {
                    throw new ServerException("题目类型错误");
                }
            } else if (PsyScoreRuleEnum.ZWJF.getCode().equals(questionRespVO.getScoreRule())) {
                // 正误计分
                if (PsyQuestionTypeEnum.Answer.getCode().equals(questionRespVO.getQuestionType())) {
                    // 简答题
                    if (StrUtil.isNotBlank(item.getAnswerOptionCode())) {
                        score.addAndGet(questionRespVO.getScore().intValue());
                    }
                } else if (PsyQuestionTypeEnum.Radio.getCode().equals(questionRespVO.getQuestionType())
                        || PsyQuestionTypeEnum.Multi.getCode().equals(questionRespVO.getQuestionType())) {
                    if (isAnswerCorrect(questionRespVO.getAnswer(), item.getAnswerOptionCode())) {
                        score.addAndGet(questionRespVO.getScore().intValue());
                    }
                }
            } else if (PsyScoreRuleEnum.BJF.getCode().equals(questionRespVO.getScoreRule())) {
                score.addAndGet(0);
            }
            item.setScore(score.get());
        }
        // 计算总分
        String scoreRule = evalTableDO.getScoreRule();
        int totalScore;
        if (scoreRule.equals("1")) {
            // 累计总分
            totalScore = items.stream().mapToInt(AnswerCardDTO.AnswerCardItemDTO::getScore).sum();
        } else if (scoreRule.equals("2")) {
            totalScore = 0;
        } else if (scoreRule.equals("3")) {
            totalScore = 0;
        } else {
            throw new ServerException("暂不支持的计分规则");
        }
        List<ResultInterpRuleDTO> list = JSONUtil.toList(evalTableDO.getResultInterpRule(), ResultInterpRuleDTO.class);
        for (ResultInterpRuleDTO ruleDTO : list) {
            ResultInterpRuleDTO dto = ruleDTO.getDTO(totalScore);
            if (dto != null) {
                answerCardDTO.setResult(dto.getResult());
                break;
            }
        }
        answerCardDTO.setTotalScore(totalScore);
    }

    @Override
    public EvalTablePrewCommonAnswerRespVO tablePreviewCommonAndAnswer(String tableId, String answerId) {
        EvalPlanPushRecordAnswerDO dtk = evalPlanPushRecordAnswerService.getById(answerId);
        if (dtk == null) {
            throw new ServerException("答案不存在");
        }
        EvalTablePrewCommonAnswerRespVO respVO = new EvalTablePrewCommonAnswerRespVO();
        EvalTablePrewCommonRespVO evalTablePrewCommonRespVO = tablePreviewCommon(tableId);
        BeanUtils.copyProperties(evalTablePrewCommonRespVO, respVO);

        List<AnswerCardDTO.AnswerCardItemDTO> items = JSONUtil.toList(dtk.getSubmitAnswer(), AnswerCardDTO.AnswerCardItemDTO.class);
        Map<String, AnswerCardDTO.AnswerCardItemDTO> questionMap = items.stream()
                .collect(Collectors.toMap(AnswerCardDTO.AnswerCardItemDTO::getQuestionId, Function.identity()));
        // 单选题
        QuestionDTO dxt = evalTablePrewCommonRespVO.getDxt();
        buildQuestion(dxt, questionMap);
        respVO.setDxt(dxt);
        // 多选题
        QuestionDTO fxt = evalTablePrewCommonRespVO.getFxt();
        buildQuestion(fxt, questionMap);
        respVO.setFxt(fxt);
        // 简答题
        QuestionDTO jdt = evalTablePrewCommonRespVO.getJdt();
        buildQuestion(jdt, questionMap);
        respVO.setJdt(jdt);

        respVO.setEvalResults(dtk.getEvalResults());
        respVO.setTimeSpentAnswer(dtk.getTimeSpentAnswer());
        respVO.setScore(dtk.getScore());
        return respVO;
    }

    @Override
    public EvalTablePrewSimpleAnswerRespVO tablePreviewSimpleAndAnswer(String tableId, String answerId, Integer sortOrder) {
        EvalPlanPushRecordAnswerDO dtk = evalPlanPushRecordAnswerService.getById(answerId);
        if (dtk == null) {
            throw new ServerException("答案不存在");
        }
        EvalTablePrewSimpleRespVO evalTablePrewSimpleRespVO = tablePreviewSimple(tableId, sortOrder);
        EvalTablePrewSimpleAnswerRespVO respVO = new EvalTablePrewSimpleAnswerRespVO();
        BeanUtils.copyProperties(evalTablePrewSimpleRespVO, respVO);
        List<AnswerCardDTO.AnswerCardItemDTO> items = JSONUtil.toList(dtk.getSubmitAnswer(), AnswerCardDTO.AnswerCardItemDTO.class);
        Map<String, AnswerCardDTO.AnswerCardItemDTO> questionMap = items.stream()
                .collect(Collectors.toMap(AnswerCardDTO.AnswerCardItemDTO::getQuestionId, Function.identity()));
        AnswerCardDTO.AnswerCardItemDTO itemDTO = questionMap.get(respVO.getId());
        if (itemDTO != null) {
            respVO.setAnswer(itemDTO.getAnswerOptionCode());
            List<EvalTableOptionRespVO> options = respVO.getOptions();
            if (CollUtil.isNotEmpty(options)) {
                Map<String, EvalTableOptionRespVO> optionMap = options.stream().collect(Collectors
                        .toMap(option -> option.getOptionCode().toLowerCase(), Function.identity()));
                List<String> answerOptions = normalizeAnswer(itemDTO.getAnswerOptionCode());
                for (String optionCode : answerOptions) {
                    EvalTableOptionRespVO optionRespVO = optionMap.get(optionCode.toLowerCase());
                    if (optionRespVO != null) {
                        optionRespVO.setSelected(true);
                    }
                }
            }
        }
        respVO.setScore(dtk.getScore());
        respVO.setEvalResults(dtk.getEvalResults());
        respVO.setTimeSpentAnswer(dtk.getTimeSpentAnswer());
        return respVO;
    }

    private void buildQuestion(QuestionDTO dxt, Map<String, AnswerCardDTO.AnswerCardItemDTO> questionMap) {
        if (dxt == null) {
            return;
        }
        List<QuestionDTO.Question> questions = dxt.getQuestions();
        if (CollUtil.isEmpty(questions)) {
            return;
        }
        for (QuestionDTO.Question question : questions) {
            AnswerCardDTO.AnswerCardItemDTO itemDTO = questionMap.get(question.getQuestionId());
            List<QuestionDTO.Option> options = question.getOptions();
            if (CollUtil.isNotEmpty(options)) {
                Map<String, QuestionDTO.Option> optionMap = options.stream().collect(Collectors
                        .toMap(option -> option.getOptionCode().toLowerCase(), Function.identity()));
                List<String> answerOptions = normalizeAnswer(itemDTO.getAnswerOptionCode());
                for (String optionCode : answerOptions) {
                    QuestionDTO.Option option = optionMap.get(optionCode.toLowerCase());
                    if (option != null) {
                        option.setSelected(true);
                    }
                }
            }
            question.setActualScore(itemDTO.getScore());
            question.setAnswer(itemDTO.getAnswerOptionCode());
        }
    }

    /**
     * 判断答案是否正确
     *
     * @param correctAnswer 标准答案
     * @param userAnswer    用户答案
     * @return
     */
    public static boolean isAnswerCorrect(String correctAnswer, String userAnswer) {
        // 处理空值情况
        if (correctAnswer == null || userAnswer == null) {
            return false;
        }
        // 标准化处理：转为小写、去除空格、分割为数组
        List<String> correctOptions = normalizeAnswer(correctAnswer);
        List<String> userOptions = normalizeAnswer(userAnswer);
        // 排序后比较数组是否完全一致
        return correctOptions.equals(userOptions);
    }

    /**
     * 标准化答案：转为小写、去除空格、分割为列表
     */
    private static List<String> normalizeAnswer(String answer) {
        return Arrays.stream(answer.toLowerCase().replace(" ", "").split(","))
                .filter(s -> !s.isEmpty()) // 过滤空字符串
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }

    private Integer getScore(String anwserOptionCode, Map<String, EvalTableOptionRespVO> optionMap) {
        EvalTableOptionRespVO optionRespVO = optionMap.get(anwserOptionCode);
        if (optionRespVO == null) {
            throw new ServerException("选项不存在");
        }
        return optionRespVO.getScore().intValue();
    }


}


