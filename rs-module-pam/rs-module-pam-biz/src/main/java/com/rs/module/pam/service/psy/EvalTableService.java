package com.rs.module.pam.service.psy;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableListReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTablePageReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTablePrewCommonAnswerRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTablePrewCommonRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTablePrewSimpleAnswerRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTablePrewSimpleRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableSaveReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalTableSubmitReqVO;
import com.rs.module.pam.dto.AnswerCardDTO;
import com.rs.module.pam.entity.psy.EvalTableDO;

import java.util.List;

/**
 * 监所事务管理-心理测评量表管理 Service 接口
 *
 * <AUTHOR>
 */
public interface EvalTableService extends IBaseService<EvalTableDO> {

    /**
     * 创建监所事务管理-心理测评量表管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createEvalTable(EvalTableSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-心理测评量表管理
     *
     * @param updateReqVO 更新信息
     */
    void updateEvalTable(EvalTableSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-心理测评量表管理
     *
     * @param id 编号
     */
    void deleteEvalTable(String id);

    /**
     * 获得监所事务管理-心理测评量表管理
     *
     * @param id 编号
     * @return 监所事务管理-心理测评量表管理
     */
    EvalTableDO getEvalTable(String id);

    /**
     * 获得监所事务管理-心理测评量表管理分页
     *
     * @param pageReqVO 分页查询
     * @return 监所事务管理-心理测评量表管理分页
     */
    PageResult<EvalTableDO> getEvalTablePage(EvalTablePageReqVO pageReqVO);

    /**
     * 获得监所事务管理-心理测评量表管理列表
     *
     * @param listReqVO 查询条件
     * @return 监所事务管理-心理测评量表管理列表
     */
    List<EvalTableDO> getEvalTableList(EvalTableListReqVO listReqVO);

    /**
     * 更新总题量
     *
     * @param id
     * @param totalQuestionNumber
     */
    void updateTablTtotalQuestionNumber(String id, Integer totalQuestionNumber);

    /**
     * 更新使用状态
     *
     * @param id
     * @param usageStatus
     */
    void updateUsageStatus(String id, String usageStatus);

    /**
     * 提交量表
     *
     * @param submitReqVO
     */
    void submitEvalTable(EvalTableSubmitReqVO submitReqVO);

    /**
     * 预览量表-常规
     *
     * @param id
     * @return
     */
    EvalTablePrewCommonRespVO tablePreviewCommon(String id);

    /**
     * 预览量表-极简
     *
     * @param id
     * @return
     */
    EvalTablePrewSimpleRespVO tablePreviewSimple(String id, Integer sortOrder);

    /**
     * 预览量表-极简(内屏用)
     *
     * @param id
     * @return
     */
    List<EvalTablePrewSimpleRespVO> tablePreviewSimple(String id);

    /**
     * 判题
     *
     * @param answerCardDTO
     * @return
     */
    void judgeQuestion(AnswerCardDTO answerCardDTO);

    /**
     * 常规模式带答案预览
     *
     * @param tableId
     * @param answerId
     * @return
     */
    EvalTablePrewCommonAnswerRespVO tablePreviewCommonAndAnswer(String tableId, String answerId);

    /**
     * 极简模式带答案预览
     * @param tableId
     * @param answerId
     * @param sortOrder
     * @return
     */
    EvalTablePrewSimpleAnswerRespVO tablePreviewSimpleAndAnswer(String tableId, String answerId, Integer sortOrder);

}
