package com.rs.module.ihc.controller.admin.md.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 药品顾送管理-药品顾送申请 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class MedicineDeliveryApplyRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("期望送药开始日期")
    private Date expectedStartDate;
    @ApiModelProperty("期望送药结束日期")
    private Date expectedEndDate;
    @ApiModelProperty("顾送药来源")
    private String drugSource;
    @ApiModelProperty("顾送药原因")
    private String deliveryReason;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("是否一致")
    private Short isConsistent;
    @ApiModelProperty("不一致原因")
    private String reasonForInconsistency;
    @ApiModelProperty("药品照片URL")
    private String imgUrl;
    @ApiModelProperty("送药日期")
    private Date deliveryDate;
    @ApiModelProperty("送药备注")
    private String deliveryRemark;
    @ApiModelProperty("异常原因")
    private String exceptionReason;
    @ApiModelProperty("异常备注")
    private String exceptionRemark;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;
}
