package com.rs.module.ihc.dao.md;

import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.PageParam;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.ihc.entity.md.MedicineDeliveryDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
 * 药品顾送管理-药品顾送申请关联药品
 *
 * <AUTHOR>
 */
@Mapper
public interface MedicineDeliveryDao extends IBaseDao<MedicineDeliveryDO> {

    default List<MedicineDeliveryDO> selectListByApplyId(String applyId) {
        return selectList(new LambdaQueryWrapperX<MedicineDeliveryDO>().eq(MedicineDeliveryDO::getApplyId, applyId));
    }

    default int deleteByApplyId(String applyId) {
        return delete(new LambdaQueryWrapperX<MedicineDeliveryDO>().eq(MedicineDeliveryDO::getApplyId, applyId));
    }

}
