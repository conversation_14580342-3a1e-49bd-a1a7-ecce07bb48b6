package com.rs.module.ihc.entity.md;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 药品顾送管理-药品顾送申请 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_md_medicine_delivery_apply")
@KeySequence("ihc_md_medicine_delivery_apply_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ihc_md_medicine_delivery_apply")
public class MedicineDeliveryApplyDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 期望送药开始日期
     */
    private Date expectedStartDate;
    /**
     * 期望送药结束日期
     */
    private Date expectedEndDate;
    /**
     * 顾送药来源
     */
    private String drugSource;
    /**
     * 顾送药原因
     */
    private String deliveryReason;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否一致
     */
    private Short isConsistent;
    /**
     * 不一致原因
     */
    private String reasonForInconsistency;
    /**
     * 药品照片URL
     */
    private String imgUrl;
    /**
     * 送药日期
     */
    private Date deliveryDate;
    /**
     * 送药备注
     */
    private String deliveryRemark;
    /**
     * 异常原因
     */
    private String exceptionReason;
    /**
     * 异常备注
     */
    private String exceptionRemark;
    /**
     * 状态
     */
    private String status;
    /**
     * ACT流程实例Id
     */
    private String actInstId;
    /**
     * 任务ID
     */
    private String taskId;

}
