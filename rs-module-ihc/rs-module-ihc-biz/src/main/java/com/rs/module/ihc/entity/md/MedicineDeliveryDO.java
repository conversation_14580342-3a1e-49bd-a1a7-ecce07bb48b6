package com.rs.module.ihc.entity.md;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 药品顾送管理-药品顾送申请关联药品 DO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 药品顾送管理-药品顾送申请关联药品新增/修改 Request VO")
@TableName("ihc_md_medicine_delivery")
@KeySequence("ihc_md_medicine_delivery_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ihc_md_medicine_delivery")
public class MedicineDeliveryDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;
    /**
     * 药品顾送申请
     */
    @ApiModelProperty("药品顾送申请")
    private String applyId;
    /**
     * 药品名称
     */
    @ApiModelProperty("药品名称")
    private String medicineName;
    /**
     * 剂型
     */
    @ApiModelProperty("剂型")
    private String dosageForm;
    /**
     * 规格
     */
    @ApiModelProperty("规格")
    private String specs;
    /**
     * 生产单位
     */
    @ApiModelProperty("生产单位")
    private String productUnit;
    /**
     * 批准文号
     */
    @ApiModelProperty("批准文号")
    private String approvalNum;
    /**
     * 原批准文号
     */
    @ApiModelProperty("原批准文号")
    private String originalApprovalNum;
    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private BigDecimal totalNum;
    /**
     * 药品库-药品id
     */
    @ApiModelProperty("药品库-药品id")
    private String medicineId;

}
