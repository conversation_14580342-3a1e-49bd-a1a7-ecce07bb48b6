package com.rs.module.ihc.controller.admin.md.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 药品顾送管理-药品顾送登记")
@Data
@EqualsAndHashCode(callSuper = true)
public class MedicineDeliveryApplyRegInfoReqVO extends BaseVO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("是否一致  1是，0否 字典：ZD_DOSAGE_FORM")
    private Short isConsistent;

    @ApiModelProperty("不一致原因")
    private String reasonForInconsistency;

    @ApiModelProperty("实际送药-药品名称")
    private String deliveryMedicineName;

    @ApiModelProperty("实际送药--剂型（字典:ZD_DOSAGE_FORM ）")
    private String deliveryDosageForm;

    @ApiModelProperty("实际送药-规格")
    private String deliverySpecs;

    @ApiModelProperty("实际送药-生产单位")
    private String deliveryProductUnit;

    @ApiModelProperty("实际送药-批准文号")
    private String deliveryApprovalNum;

    @ApiModelProperty("实际送药-原批准文号")
    private String deliveryOriginalApprovalNum;

    @ApiModelProperty("实际送药-数量")
    private BigDecimal deliveryTotalNum;

    @ApiModelProperty("有效期开始")
    private Date expiryStartDate;

    @ApiModelProperty("有效期结束")
    private Date expiryEndDate;

    @ApiModelProperty("药品照片URL")
    private String imgUrl;

    @ApiModelProperty("送药日期")
    private Date deliveryDate;

    @ApiModelProperty("送药备注")
    private String deliveryRemark;


}
