package com.rs.module.ihc.controller.admin.md.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 药品顾送管理-药品顾送登记-异常终止")
@Data
@EqualsAndHashCode(callSuper = true)
public class MedicineDeliveryApplyAbnormalReqVO extends BaseVO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("送药备注")
    private String deliveryRemark;

    @ApiModelProperty("异常原因 ZD_YPGS_YCYY ")
    private String exceptionReason;


}
