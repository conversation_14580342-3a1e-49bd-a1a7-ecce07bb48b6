package com.rs.module.ihc.service.md;

import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.vo.SimpleApproveReqVO;
import com.rs.module.ihc.controller.admin.md.vo.*;
import com.rs.module.ihc.dao.md.MedicineDeliveryApplyDao;
import com.rs.module.ihc.dao.md.MedicineDeliveryDao;
import com.rs.module.ihc.entity.md.MedicineDeliveryApplyDO;
import com.rs.module.ihc.entity.md.MedicineDeliveryDO;
import com.rs.module.ihc.enums.ApplyStatusEnum;
import com.rs.module.ihc.enums.MedicineDeliveryApplyStatusEnum;
import com.rs.module.ihc.service.approval.ApprovalServiceImpl;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 药品顾送管理-药品顾送申请 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Log4j2
public class MedicineDeliveryApplyServiceImpl extends ApprovalServiceImpl<MedicineDeliveryApplyDao, MedicineDeliveryApplyDO> implements MedicineDeliveryApplyService {

    @Resource
    private MedicineDeliveryApplyDao medicineDeliveryApplyDao;
    @Resource
    private MedicineDeliveryDao medicineDeliveryDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createMedicineDeliveryApply(MedicineDeliveryApplySaveReqVO createReqVO) {
        // 插入
        MedicineDeliveryApplyDO medicineDeliveryApply = BeanUtils.toBean(createReqVO, MedicineDeliveryApplyDO.class);
        medicineDeliveryApplyDao.insert(medicineDeliveryApply);

        // 插入子表
        createMedicineDeliveryList(medicineDeliveryApply.getId(), createReqVO.getMedicineDeliverys());

        //启动流程
        String busType = MsgBusTypeEnum.YL_LDXS.getCode();
        MapBuilder<String, Object> variables = MapUtil.builder();
        variables.put("ywbh", medicineDeliveryApply.getId()).put("busType", busType);
        startProcess(variables, medicineDeliveryApply);
        return medicineDeliveryApply.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMedicineDeliveryApply(MedicineDeliveryApplySaveReqVO updateReqVO) {
        // 校验存在
        validateMedicineDeliveryApplyExists(updateReqVO.getId());
        // 更新
        MedicineDeliveryApplyDO updateObj = BeanUtils.toBean(updateReqVO, MedicineDeliveryApplyDO.class);
        medicineDeliveryApplyDao.updateById(updateObj);

        // 更新子表
        updateMedicineDeliveryList(updateReqVO.getId(), updateReqVO.getMedicineDeliverys());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMedicineDeliveryApply(String id) {
        // 校验存在
        validateMedicineDeliveryApplyExists(id);
        // 删除
        medicineDeliveryApplyDao.deleteById(id);

        // 删除子表
        deleteMedicineDeliveryByApplyId(id);
    }

    private void validateMedicineDeliveryApplyExists(String id) {
        if (medicineDeliveryApplyDao.selectById(id) == null) {
            throw new ServerException("药品顾送管理-药品顾送申请数据不存在");
        }
    }

    @Override
    public MedicineDeliveryApplyDO getMedicineDeliveryApply(String id) {
        return medicineDeliveryApplyDao.selectById(id);
    }

    @Override
    public PageResult<MedicineDeliveryApplyDO> getMedicineDeliveryApplyPage(MedicineDeliveryApplyPageReqVO pageReqVO) {
        return medicineDeliveryApplyDao.selectPage(pageReqVO);
    }

    @Override
    public List<MedicineDeliveryApplyDO> getMedicineDeliveryApplyList(MedicineDeliveryApplyListReqVO listReqVO) {
        return medicineDeliveryApplyDao.selectList(listReqVO);
    }


    // ==================== 子表（药品顾送管理-药品顾送申请关联药品） ====================

    @Override
    public List<MedicineDeliveryDO> getMedicineDeliveryListByApplyId(String applyId) {
        return medicineDeliveryDao.selectListByApplyId(applyId);
    }

    @Override
    public void regInfo(MedicineDeliveryApplyRegInfoReqVO regInfoReqVO) {

        MedicineDeliveryApplyDO medicineDeliveryApplyDO = medicineDeliveryApplyDao.selectById(regInfoReqVO.getId());
        Assert.notNull( medicineDeliveryApplyDO, "药品顾送申请数据不存在");
        BeanUtils.copyProperties(regInfoReqVO, medicineDeliveryApplyDO);
        medicineDeliveryApplyDO.setStatus(MedicineDeliveryApplyStatusEnum.YWC.getCode() );
        medicineDeliveryApplyDao.updateById(medicineDeliveryApplyDO);

    }

    @Override
    public void abnormalRegInfo(MedicineDeliveryApplyAbnormalReqVO regInfoReqVO) {
        MedicineDeliveryApplyDO medicineDeliveryApplyDO = medicineDeliveryApplyDao.selectById(regInfoReqVO.getId());
        Assert.notNull( medicineDeliveryApplyDO, "药品顾送申请数据不存在");
        BeanUtils.copyProperties(regInfoReqVO, medicineDeliveryApplyDO);
        medicineDeliveryApplyDO.setStatus(MedicineDeliveryApplyStatusEnum.YZZ.getCode() );
        medicineDeliveryApplyDao.updateById(medicineDeliveryApplyDO);
    }

    private void createMedicineDeliveryList(String applyId, List<MedicineDeliveryDO> list) {
        list.forEach(o -> o.setApplyId(applyId));
        list.forEach(o -> medicineDeliveryDao.insert(o));
    }

    private void updateMedicineDeliveryList(String applyId, List<MedicineDeliveryDO> list) {
        deleteMedicineDeliveryByApplyId(applyId);
        list.forEach(o -> o.setId(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createMedicineDeliveryList(applyId, list);
    }

    private void deleteMedicineDeliveryByApplyId(String applyId) {
        medicineDeliveryDao.deleteByApplyId(applyId);
    }


    @Override
    public String getUrl(MedicineDeliveryApplyDO entityDO) {
        return StrUtil.format("/#/discipline/ypgs?curId={}&saveType=approve", entityDO.getId());
    }

    @Override
    public String getDefKey() {
        return "yaopingusongliucheng";
    }

    @Override
    public String getMsgTitle(MedicineDeliveryApplyDO entity) {
        return "【审批】药品顾送";
    }

    @Override
    public void startSuccess(MedicineDeliveryApplyDO entity, JSONObject result) {
        entity.setStatus(ApplyStatusEnum.DSP.getCode());
        medicineDeliveryApplyDao.updateById(entity);
    }

    @Override
    public void startFail(MedicineDeliveryApplyDO entity, JSONObject result) {

    }

    @Override
    public void approvePassedSuccess(MedicineDeliveryApplyDO entity, SimpleApproveReqVO approveReqVO, JSONObject result) {

    }

    @Override
    public void approveNotPassedEndSuccess(MedicineDeliveryApplyDO entity, SimpleApproveReqVO approveReqVO, JSONObject result) {

    }

    @Override
    public void approvePassedFail(MedicineDeliveryApplyDO entity, SimpleApproveReqVO approveReqVO, JSONObject result) {

    }

    @Override
    public void approveNotPassedEndFail(MedicineDeliveryApplyDO entity, SimpleApproveReqVO approveReqVO, JSONObject result) {

    }

    @Override
    public void finishNotPassedEnd(MedicineDeliveryApplyDO entity, SimpleApproveReqVO approveReqVO) {
        entity.setStatus(ApplyStatusEnum.YZZ.getCode());
        medicineDeliveryApplyDao.updateById(entity);
    }

    @Override
    public void finishPassed(MedicineDeliveryApplyDO entity, SimpleApproveReqVO approveReqVO) {
        entity.setStatus(MedicineDeliveryApplyStatusEnum.DGS.getCode());
        medicineDeliveryApplyDao.updateById(entity);
    }

}
