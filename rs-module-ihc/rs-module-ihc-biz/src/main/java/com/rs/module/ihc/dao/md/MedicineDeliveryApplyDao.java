package com.rs.module.ihc.dao.md;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.md.MedicineDeliveryApplyDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.md.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 药品顾送管理-药品顾送申请 Dao
*
* <AUTHOR>
*/
@Mapper
public interface MedicineDeliveryApplyDao extends IBaseDao<MedicineDeliveryApplyDO> {


    default PageResult<MedicineDeliveryApplyDO> selectPage(MedicineDeliveryApplyPageReqVO reqVO) {
        Page<MedicineDeliveryApplyDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<MedicineDeliveryApplyDO> wrapper = new LambdaQueryWrapperX<MedicineDeliveryApplyDO>()
            .eqIfPresent(MedicineDeliveryApplyDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(MedicineDeliveryApplyDO::getJgryxm, reqVO.getJgryxm())
            .betweenIfPresent(MedicineDeliveryApplyDO::getExpectedStartDate, reqVO.getExpectedStartDate())
            .betweenIfPresent(MedicineDeliveryApplyDO::getExpectedEndDate, reqVO.getExpectedEndDate())
            .eqIfPresent(MedicineDeliveryApplyDO::getDrugSource, reqVO.getDrugSource())
            .eqIfPresent(MedicineDeliveryApplyDO::getDeliveryReason, reqVO.getDeliveryReason())
            .eqIfPresent(MedicineDeliveryApplyDO::getRemark, reqVO.getRemark())
            .eqIfPresent(MedicineDeliveryApplyDO::getIsConsistent, reqVO.getIsConsistent())
            .eqIfPresent(MedicineDeliveryApplyDO::getReasonForInconsistency, reqVO.getReasonForInconsistency())
            .eqIfPresent(MedicineDeliveryApplyDO::getImgUrl, reqVO.getImgUrl())
            .betweenIfPresent(MedicineDeliveryApplyDO::getDeliveryDate, reqVO.getDeliveryDate())
            .eqIfPresent(MedicineDeliveryApplyDO::getDeliveryRemark, reqVO.getDeliveryRemark())
            .eqIfPresent(MedicineDeliveryApplyDO::getExceptionReason, reqVO.getExceptionReason())
            .eqIfPresent(MedicineDeliveryApplyDO::getExceptionRemark, reqVO.getExceptionRemark())
            .eqIfPresent(MedicineDeliveryApplyDO::getStatus, reqVO.getStatus())
            .eqIfPresent(MedicineDeliveryApplyDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(MedicineDeliveryApplyDO::getTaskId, reqVO.getTaskId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(MedicineDeliveryApplyDO::getAddTime);
        }
        Page<MedicineDeliveryApplyDO> medicineDeliveryApplyPage = selectPage(page, wrapper);
        return new PageResult<>(medicineDeliveryApplyPage.getRecords(), medicineDeliveryApplyPage.getTotal());
    }
    default List<MedicineDeliveryApplyDO> selectList(MedicineDeliveryApplyListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MedicineDeliveryApplyDO>()
            .eqIfPresent(MedicineDeliveryApplyDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(MedicineDeliveryApplyDO::getJgryxm, reqVO.getJgryxm())
            .betweenIfPresent(MedicineDeliveryApplyDO::getExpectedStartDate, reqVO.getExpectedStartDate())
            .betweenIfPresent(MedicineDeliveryApplyDO::getExpectedEndDate, reqVO.getExpectedEndDate())
            .eqIfPresent(MedicineDeliveryApplyDO::getDrugSource, reqVO.getDrugSource())
            .eqIfPresent(MedicineDeliveryApplyDO::getDeliveryReason, reqVO.getDeliveryReason())
            .eqIfPresent(MedicineDeliveryApplyDO::getRemark, reqVO.getRemark())
            .eqIfPresent(MedicineDeliveryApplyDO::getIsConsistent, reqVO.getIsConsistent())
            .eqIfPresent(MedicineDeliveryApplyDO::getReasonForInconsistency, reqVO.getReasonForInconsistency())
            .eqIfPresent(MedicineDeliveryApplyDO::getImgUrl, reqVO.getImgUrl())
            .betweenIfPresent(MedicineDeliveryApplyDO::getDeliveryDate, reqVO.getDeliveryDate())
            .eqIfPresent(MedicineDeliveryApplyDO::getDeliveryRemark, reqVO.getDeliveryRemark())
            .eqIfPresent(MedicineDeliveryApplyDO::getExceptionReason, reqVO.getExceptionReason())
            .eqIfPresent(MedicineDeliveryApplyDO::getExceptionRemark, reqVO.getExceptionRemark())
            .eqIfPresent(MedicineDeliveryApplyDO::getStatus, reqVO.getStatus())
            .eqIfPresent(MedicineDeliveryApplyDO::getActInstId, reqVO.getActInstId())
            .eqIfPresent(MedicineDeliveryApplyDO::getTaskId, reqVO.getTaskId())
        .orderByDesc(MedicineDeliveryApplyDO::getAddTime));    }


    }
