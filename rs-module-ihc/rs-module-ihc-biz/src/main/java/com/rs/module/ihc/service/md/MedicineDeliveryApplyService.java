package com.rs.module.ihc.service.md;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.md.vo.*;
import com.rs.module.ihc.entity.md.MedicineDeliveryApplyDO;
import com.rs.module.ihc.entity.md.MedicineDeliveryDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.ihc.service.approval.ApprovalService;

/**
 * 药品顾送管理-药品顾送申请 Service 接口
 *
 * <AUTHOR>
 */
public interface MedicineDeliveryApplyService extends ApprovalService<MedicineDeliveryApplyDO> {

    /**
     * 创建药品顾送管理-药品顾送申请
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMedicineDeliveryApply(@Valid MedicineDeliveryApplySaveReqVO createReqVO);

    /**
     * 更新药品顾送管理-药品顾送申请
     *
     * @param updateReqVO 更新信息
     */
    void updateMedicineDeliveryApply(@Valid MedicineDeliveryApplySaveReqVO updateReqVO);

    /**
     * 删除药品顾送管理-药品顾送申请
     *
     * @param id 编号
     */
    void deleteMedicineDeliveryApply(String id);

    /**
     * 获得药品顾送管理-药品顾送申请
     *
     * @param id 编号
     * @return 药品顾送管理-药品顾送申请
     */
    MedicineDeliveryApplyDO getMedicineDeliveryApply(String id);

    /**
    * 获得药品顾送管理-药品顾送申请分页
    *
    * @param pageReqVO 分页查询
    * @return 药品顾送管理-药品顾送申请分页
    */
    PageResult<MedicineDeliveryApplyDO> getMedicineDeliveryApplyPage(MedicineDeliveryApplyPageReqVO pageReqVO);

    /**
    * 获得药品顾送管理-药品顾送申请列表
    *
    * @param listReqVO 查询条件
    * @return 药品顾送管理-药品顾送申请列表
    */
    List<MedicineDeliveryApplyDO> getMedicineDeliveryApplyList(MedicineDeliveryApplyListReqVO listReqVO);


    // ==================== 子表（药品顾送管理-药品顾送申请关联药品） ====================

    /**
     * 获得药品顾送管理-药品顾送申请关联药品列表
     *
     * @param applyId 药品顾送申请
     * @return 药品顾送管理-药品顾送申请关联药品列表
     */
    List<MedicineDeliveryDO> getMedicineDeliveryListByApplyId(String applyId);

    void regInfo(MedicineDeliveryApplyRegInfoReqVO regInfoReqVO);
    void abnormalRegInfo(MedicineDeliveryApplyAbnormalReqVO regInfoReqVO);


}
