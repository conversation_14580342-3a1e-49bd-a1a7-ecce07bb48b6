#!/bin/bash

# 测试显示修复功能

# 清理显示值的函数
clean_display_value() {
    local value="$1"
    # 移除所有控制字符、换行符、回车符，并清理前后空格
    echo "$value" | tr -d '\r\n\t' | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//' | tr -d '\033\007\010'
}

echo "=========================================="
echo "  测试显示修复功能"
echo "=========================================="
echo ""

# 模拟各种可能的问题值
test_values=(
    "*************"
    "************* "
    " *************"
    "*************\n"
    "*************\r\n"
    "*************\t"
    $'*************\r'
    $'*************\n'
    $'\t*************\t'
    "未找到"
    ""
)

echo "测试各种可能的配置值："
for i in "${!test_values[@]}"; do
    original="${test_values[i]}"
    cleaned=$(clean_display_value "$original")
    
    echo "测试 $((i+1)):"
    echo "  原始值: '${original}' (长度: ${#original})"
    echo "  清理后: '${cleaned}' (长度: ${#cleaned})"
    echo "  显示测试: [当前: ${cleaned}]"
    echo ""
done

echo "交互式测试："
echo "以下是模拟的用户输入提示："
echo ""

# 模拟实际的用户输入提示
CURRENT_SERVER_IP="*************"
CURRENT_ACP_PORT="9100"
CURRENT_IGNORES="/doc.html/**,/acp/app/msg/js/**"

echo "=== 服务器配置 ==="
echo -n "服务器IP地址 [当前: $(clean_display_value "$CURRENT_SERVER_IP")]: "
echo "(光标应该在这里)"
echo -n "ACP服务端口 [当前: $(clean_display_value "$CURRENT_ACP_PORT")]: "
echo "(光标应该在这里)"
echo ""

echo "=== 系统配置 ==="
echo -n "忽略路径配置 [当前: $(clean_display_value "$CURRENT_IGNORES")]: "
echo "(光标应该在这里)"
echo ""

echo "如果上面的显示正常，光标都在行尾，那么修复成功！"
