#!/bin/bash

# 测试YAML配置更新脚本
# 用于验证修复后的配置更新功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 更安全的YAML配置更新函数
update_yaml_config() {
    local file_path="$1"
    local config_path="$2"
    local old_value="$3"
    local new_value="$4"
    local description="$5"
    
    if [ -z "$new_value" ] || [ -z "$old_value" ]; then
        return 1
    fi
    
    print_info "正在更新 $description: $old_value → $new_value"
    
    # 转义特殊字符
    local escaped_old=$(echo "$old_value" | sed 's/[\[\.*^$()+?{|]/\\&/g' | sed 's|/|\\/|g')
    local escaped_new=$(echo "$new_value" | sed 's/[\[\.*^$()+?{|]/\\&/g' | sed 's|/|\\/|g')
    
    # 根据配置路径进行精确替换
    case "$config_path" in
        "nacos.ip")
            # 只替换nacos配置块中的ip
            sed -i "/nacos:/,/group:/ s/ip: $escaped_old/ip: $escaped_new/" "$file_path"
            ;;
        "redis.host")
            # 只替换redis配置块中的host
            sed -i "/redis:/,/lettuce:/ s/host: $escaped_old/host: $escaped_new/" "$file_path"
            ;;
        "postgresql.url")
            # 只替换PostgreSQL URL中的IP
            sed -i "s|postgresql://$escaped_old|postgresql://$escaped_new|g" "$file_path"
            ;;
        "mysql.url")
            # 只替换MySQL URL中的IP
            sed -i "s|mysql://$escaped_old|mysql://$escaped_new|g" "$file_path"
            ;;
        "mongodb.uri")
            # 只替换MongoDB URI中的IP
            sed -i "s|mongodb://$escaped_old|mongodb://$escaped_new|g" "$file_path"
            ;;
        *)
            print_warning "未知的配置路径: $config_path"
            return 1
            ;;
    esac
    
    if [ $? -eq 0 ]; then
        print_success "✓ 已更新 $description"
        return 0
    else
        print_error "✗ 更新失败 $description"
        return 1
    fi
}

# 验证配置更新是否成功
verify_config_update() {
    local file_path="$1"
    local config_path="$2"
    local expected_value="$3"
    
    case "$config_path" in
        "nacos.ip")
            local actual_value=$(awk '/nacos:/,/group:/ {if(/ip:/) print}' "$file_path" | head -1 | sed 's/.*ip: *//' | sed 's/ *$//')
            ;;
        "redis.host")
            local actual_value=$(awk '/redis:/,/lettuce:/ {if(/host:/) print}' "$file_path" | head -1 | sed 's/.*host: *//' | sed 's/ *$//')
            ;;
        *)
            print_warning "验证功能暂不支持配置路径: $config_path"
            return 1
            ;;
    esac
    
    if [ "$actual_value" = "$expected_value" ]; then
        print_success "✓ 验证成功: $config_path = $expected_value"
        return 0
    else
        print_error "✗ 验证失败: $config_path 期望值=$expected_value, 实际值=$actual_value"
        return 1
    fi
}

# 测试单个配置文件
test_config_file() {
    local config_file="$1"
    local old_ip="$2"
    local new_ip="$3"
    
    if [ ! -f "$config_file" ]; then
        print_error "配置文件不存在: $config_file"
        return 1
    fi
    
    print_info "测试配置文件: $config_file"
    
    # 备份原文件
    local backup_file="${config_file}.test_backup.$(date +%Y%m%d_%H%M%S)"
    cp "$config_file" "$backup_file"
    print_info "已备份文件: $backup_file"
    
    # 更新配置
    local success_count=0
    local total_count=0
    
    # 测试Nacos IP更新
    if grep -q "nacos:" "$config_file"; then
        ((total_count++))
        if update_yaml_config "$config_file" "nacos.ip" "$old_ip" "$new_ip" "Nacos服务器IP"; then
            if verify_config_update "$config_file" "nacos.ip" "$new_ip"; then
                ((success_count++))
            fi
        fi
    fi
    
    # 测试Redis Host更新
    if grep -q "redis:" "$config_file"; then
        ((total_count++))
        if update_yaml_config "$config_file" "redis.host" "$old_ip" "$new_ip" "Redis服务器IP"; then
            if verify_config_update "$config_file" "redis.host" "$new_ip"; then
                ((success_count++))
            fi
        fi
    fi
    
    # 测试PostgreSQL URL更新
    if grep -q "postgresql://" "$config_file"; then
        ((total_count++))
        if update_yaml_config "$config_file" "postgresql.url" "$old_ip" "$new_ip" "PostgreSQL服务器IP"; then
            ((success_count++))
        fi
    fi
    
    # 测试MySQL URL更新
    if grep -q "mysql://" "$config_file"; then
        ((total_count++))
        if update_yaml_config "$config_file" "mysql.url" "$old_ip" "$new_ip" "MySQL服务器IP"; then
            ((success_count++))
        fi
    fi
    
    # 测试MongoDB URI更新
    if grep -q "mongodb://" "$config_file"; then
        ((total_count++))
        if update_yaml_config "$config_file" "mongodb.uri" "$old_ip" "$new_ip" "MongoDB服务器IP"; then
            ((success_count++))
        fi
    fi
    
    # 显示测试结果
    echo ""
    print_info "测试结果: $success_count/$total_count 项配置更新成功"
    
    # 恢复原文件
    mv "$backup_file" "$config_file"
    print_info "已恢复原文件"
    
    return 0
}

# 主函数
main() {
    echo "=========================================="
    echo "  YAML配置更新测试工具"
    echo "=========================================="
    echo ""
    
    # 获取用户输入
    read -p "请输入要测试的配置文件路径: " config_file
    read -p "请输入当前IP地址: " old_ip
    read -p "请输入新的IP地址: " new_ip
    
    if [ -z "$config_file" ] || [ -z "$old_ip" ] || [ -z "$new_ip" ]; then
        print_error "参数不能为空"
        exit 1
    fi
    
    # 执行测试
    test_config_file "$config_file" "$old_ip" "$new_ip"
    
    echo ""
    print_success "测试完成！"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
