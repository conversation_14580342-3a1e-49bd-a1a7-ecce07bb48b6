#!/bin/bash

# 简单的文件查找测试

echo "=========================================="
echo "  简单文件查找测试"
echo "=========================================="
echo ""

echo "当前目录: $(pwd)"
echo ""

echo "1. 直接查找 application-conf.yml:"
find . -name "application-conf.yml" -type f

echo ""
echo "2. 统计数量:"
count=$(find . -name "application-conf.yml" -type f | wc -l)
echo "找到 $count 个文件"

echo ""
echo "3. 检查特定路径:"
echo "检查 rs-acp/config/:"
if [ -f "rs-acp/config/application-conf.yml" ]; then
    echo "  ✓ rs-acp/config/application-conf.yml 存在"
else
    echo "  ✗ rs-acp/config/application-conf.yml 不存在"
fi

echo "检查 rs-dam/config/:"
if [ -f "rs-dam/config/application-conf.yml" ]; then
    echo "  ✓ rs-dam/config/application-conf.yml 存在"
else
    echo "  ✗ rs-dam/config/application-conf.yml 不存在"
fi

echo ""
echo "4. 数组测试:"
files=($(find . -name "application-conf.yml" -type f))
echo "数组长度: ${#files[@]}"
for i in "${!files[@]}"; do
    echo "  文件 $((i+1)): ${files[i]}"
done

echo ""
echo "5. 目录结构:"
echo "当前目录下的子目录:"
ls -d */ 2>/dev/null || echo "没有子目录"
