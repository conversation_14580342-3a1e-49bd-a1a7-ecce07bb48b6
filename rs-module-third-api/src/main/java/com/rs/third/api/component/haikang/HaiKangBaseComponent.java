package com.rs.third.api.component.haikang;

import com.alibaba.fastjson.JSON;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.constant.Constants;
import com.rs.third.api.config.HaiKangProperties;
import com.rs.third.api.utils.DoorResourceMapGenerator;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 海康基础组件
 *
 * <AUTHOR>
 * @Date 2025/7/23 15:53
 */
@Slf4j
public class HaiKangBaseComponent {

    /**
     * 根据需求调整超时时间
     */
    static {
        //连接超时时间
        Constants.DEFAULT_TIMEOUT = 10000;
        //读取超时时间
        Constants.SOCKET_TIMEOUT = 60000;
    }

    private HaiKangProperties properties;

    public HaiKangBaseComponent(HaiKangProperties properties) {
        this.properties = properties;
    }

    public HaiKangProperties getProperties() {
        return properties;
    }

    public String sendRequest(Map<String, Object> paramMap, String url) throws Exception {
        String body = JSON.toJSON(paramMap).toString();
        final String getCamsApi = properties.getArtemisPath() + url;
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", getCamsApi);
            }
        };
        if (properties.getConfig().getAppKey().contains("xxxx")) {
            // 模拟海康接口
            if (url.equals("/api/acs/v1/door/doControl")) {
                List<String> doorIndexCodes = (List) paramMap.get("doorIndexCodes");
                return DoorResourceMapGenerator.getControlData(doorIndexCodes.get(0));
            }
            if (url.equals("/api/acs/v1/door/states")) {
                return DoorResourceMapGenerator.getStatesData(paramMap);
            }
            if (url.equals("/api/resource/v2/door/search")) {
                return DoorResourceMapGenerator.getData();
            }
            if (url.equals("/api/resource/v2/door/search")) {
                return DoorResourceMapGenerator.getData();
            }
            if (url.equals("/api/acs/v2/door/events")) {
                return DoorResourceMapGenerator.getEventData();
            }
            return null;
        } else {
            return ArtemisHttpUtil.doPostStringArtemis(properties.getConfig(), path, body, null, null, "application/json");
        }
    }

    protected String formatISO8601WithTimezone(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX");
        return sdf.format(date);
    }


}
