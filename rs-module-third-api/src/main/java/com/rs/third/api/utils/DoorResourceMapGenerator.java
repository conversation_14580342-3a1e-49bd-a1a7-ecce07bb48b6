package com.rs.third.api.utils;

import com.alibaba.fastjson.JSON;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 门资源测试数据生成工具类（返回Map类型）
 */
public class DoorResourceMapGenerator {

    /**
     * 生成第三看守所的20条门资源测试数据（Map格式）
     *
     * @return Map类型的门资源列表
     */
    public static List<Map<String, Object>> generateThirdPrisonDoorResourcesAsMap() {
        List<Map<String, Object>> resources = new ArrayList<>(20);

        // 基础公共字段
        String baseRegionPathPrefix = "@root00000000@";
        String parentResourceType = "acsDevice";
        String treatyType = "isapi_net";
        String channelType = "door";
        int isCascade = 0;

        // 差异化配置（区域、名称、位置）
        String[][] regionConfigs = {
                {"西一区域", "西监区管道间04", "西一监区一层"},
                {"东二区域", "东监区禁闭室02", "东二监区一层"},
                {"南三区域", "南监区仓库03", "南三监区二层"},
                {"北四区域", "北监区武器库01", "北四监区地下一层"},
                {"中五区域", "中监区会议室05", "中五监区二层"},
                {"西南六区域", "西南监区浴室06", "西南六监区三层"},
                {"东南七区域", "东南监区食堂07", "东南七监区一层"},
                {"西北八区域", "西北监区档案室08", "西北八监区地下二层"},
                {"东北九区域", "东北监区图书室09", "东北九监区三层"},
                {"中一区域", "中监区接待室10", "中一监区一层"},
                {"中西十一区域", "中西监区活动室11", "中西十一监区二层"},
                {"中东十二区域", "中东监区医务室12", "中东十二监区四层"},
                {"南十三区域", "南监区值班室13", "南十三监区一层"},
                {"北十四区域", "北监区教育室14", "北十四监区三层"},
                {"西十五区域", "西监区仓库15", "西十五监区地下一层"},
                {"东十六区域", "东监区洗衣房16", "东十六监区二层"},
                {"南十七区域", "南监区晾衣间17", "南十七监区五层"},
                {"北十八区域", "北监区配电房18", "北十八监区四层"},
                {"西十九区域", "西监区工具房19", "西十九监区一层"},
                {"东二十区域", "东监区卫生间20", "东二十监区三层"}
        };
        String[] indexCodes = {
                "9cc509818f484bbda19139029f011e25",
                "07ab8cfd1977450f855cf61bd473b2d4",
                "6731f152163d4378b7b939989c4e56a0",
                "9e9520355ae247498a2e2f01f9e9ab48",
                "51de8916ef77402895a83a90fd08fb7b",
                "b121718a8c354f88b4ab7aecdecc639d",
                "6a1b6c05d2744d4cb84358022594aeae",
                "08aca39f6cbf43ec8c8b2bccddf5f66c",
                "19771c77fc4f4d8da18ca818b64b078c",
                "6aba45d0a6db43d7b58a3d6384b10a9f",
                "a14455caa1c64c14b8517462c808eb89",
                "a68812553c284f26a8817d42336d089e",
                "bdc20d6a24b84be88c94baec16860e70",
                "59c862a7c5864140bb928b7ae3ec083e",
                "60d0efa4f5324e83bd3187afbb4a6538",
                "583c30116aa54bd6bae95a6a01920517",
                "92fab86705a44b8c891a2c51ee8b26c6",
                "9246f42d0f214abdb3419796b124ff37",
                "54da5def728d4977a42e2dad52caef5e",
                "bf8a6952be3143ddab73d1b7620a3259"};

        for (int i = 0; i < 20; i++) {
            Map<String, Object> resource = new HashMap<>(40);

            // 生成唯一ID
            String regionIndexCode = UUID.randomUUID().toString().replace("-", "").substring(0, 32);
            String controlOneId = UUID.randomUUID().toString().replace("-", "").substring(0, 32);
            String readerInId = UUID.randomUUID().toString().replace("-", "").substring(0, 32);
            String readerOutId = UUID.randomUUID().toString().replace("-", "").substring(0, 32);

            // 差异化字段
            String regionName = regionConfigs[i][0];
            String name = regionConfigs[i][1];
            String installLocation = regionConfigs[i][2];
            int doorNo = i + 1;
            String regionPathName = "第三看守所/" + regionName;
            String regionPath = baseRegionPathPrefix + regionIndexCode + "@";
            int sort = 300 + (int) (Math.random() * 600); // 300-900随机排序值

            // 时间字段（创建时间和更新时间）
            LocalDateTime createTime = LocalDateTime.of(2025, 6, 27, 9, 0, 0).plusMinutes(i * 15);
            LocalDateTime updateTime = createTime.plusDays(14).plusMinutes(i * 10);

            // 填充Map字段
            resource.put("regionIndexCode", regionIndexCode);
            resource.put("controlTwoId", i % 3 == 0 ? null : UUID.randomUUID().toString().replace("-", "").substring(0, 32));
            resource.put("regionPathName", regionPathName);
            resource.put("channelNo", String.valueOf(doorNo));
            resource.put("indexCode", indexCodes[i]);
            resource.put("channelType", channelType);
            resource.put("fireproChannelMaxValue", null);
            resource.put("staticLon", null);
            resource.put("intelligentSet", null);
            resource.put("fireproChannelStatus", null);
            resource.put("zoneId", null);
            resource.put("fireproChannelAddressCode", null);
            resource.put("controlOneId", controlOneId);
            resource.put("parentIndexCode", controlOneId); // 父索引与控制ID一致
            resource.put("longitude", "");
            resource.put("hyperlink", null);
            resource.put("installLocation", installLocation);
            resource.put("readerOutId", readerOutId);
            resource.put("isCascade", isCascade);
            resource.put("sort", sort);
            resource.put("doorSerial", doorNo);
            resource.put("transType", null);
            resource.put("name", name);
            resource.put("readerInId", readerInId);
            resource.put("staticLat", null);
            resource.put("altitude", "");
            resource.put("fireproChannelSubType", null);
            resource.put("latitude", "");
            resource.put("fireproChannelMinValue", null);
            resource.put("regionName", regionName);
            resource.put("description", getDescriptionByRoomName(name));
            resource.put("disOrder", sort);
            resource.put("capability", null);
            resource.put("mapIndexCode", null);
            resource.put("fireproChannelType", null);
            resource.put("fireproChannelFirm", null);
            resource.put("regionPath", regionPath);
            resource.put("elevation", "");
            resource.put("fireproChannelOwnSystemId", null);
            resource.put("dataVersion", null);
            resource.put("extendJson", ""); // 空字符串
            resource.put("updateTime", updateTime);
            resource.put("treatyType", treatyType);
            resource.put("parentResourceType", parentResourceType);
            resource.put("createTime", createTime);
            resource.put("capabilitySet", null);
            resource.put("doorNo", String.valueOf(doorNo));
            resource.put("resourceType", "door");

            resources.add(resource);
        }

        return resources;
    }

    /**
     * 根据房间名称生成描述信息
     */
    private static String getDescriptionByRoomName(String roomName) {
        if (roomName.contains("管道间")) return "管道维护与检查通道";
        if (roomName.contains("禁闭室")) return "临时禁闭管理场所";
        if (roomName.contains("仓库")) return "日常物资存储仓库";
        if (roomName.contains("武器库")) return "警械装备存放点";
        if (roomName.contains("会议室")) return "日常工作会议场所";
        if (roomName.contains("浴室")) return "集体洗浴场所";
        if (roomName.contains("食堂")) return "集体就餐区域";
        if (roomName.contains("档案室")) return "档案资料存储管理";
        if (roomName.contains("图书室")) return "图书借阅与阅读区";
        if (roomName.contains("接待室")) return "家属会见接待场所";
        if (roomName.contains("活动室")) return "文体活动区域";
        if (roomName.contains("医务室")) return "基础医疗服务点";
        if (roomName.contains("值班室")) return "值班人员值守点";
        if (roomName.contains("教育室")) return "思想教育学习区";
        if (roomName.contains("洗衣房")) return "衣物清洗处理区";
        if (roomName.contains("晾衣间")) return "衣物晾晒区域";
        if (roomName.contains("配电房")) return "电力供应控制中心";
        if (roomName.contains("工具房")) return "维修工具存放点";
        if (roomName.contains("卫生间")) return "公共卫生场所";
        return "";
    }

    public static String getData() {
        Map<String, Object> data = new HashMap<>(0);
        data.put("code", "0");
        data.put("msg", "success");
        HashMap<Object, Object> objectHashMap = new HashMap<>(0);
        objectHashMap.put("total", 20);
        List<Map<String, Object>> mapList = DoorResourceMapGenerator.generateThirdPrisonDoorResourcesAsMap();
        objectHashMap.put("list", mapList);
        data.put("data", objectHashMap);
        return JSON.toJSONString(data);
    }

    public static String getEventData() {
        return "{\"code\":0,\"msg\":\"操作成功\",\"data\":{\"list\":[{\"eventId\":\"66a8c12328fc19263a3300bc5f7bc888\",\"eventName\":\"acs.acs.eventType.infoDoorLockClose\",\"eventTime\":\"2025-08-07T11:01:37+08:00\",\"personId\":null,\"cardNo\":null,\"personName\":null,\"orgIndexCode\":null,\"orgName\":null,\"doorName\":\"测试专用门禁_门_1\",\"doorIndexCode\":\"51de8916ef77402895a83a90fd08fb7b\",\"doorRegionIndexCode\":\"b3bc65c498264e649ef0338590f63c10\",\"picUri\":null,\"svrIndexCode\":null,\"eventType\":199942,\"inAndOutType\":-1,\"readerDevIndexCode\":null,\"readerDevName\":null,\"devIndexCode\":\"2a659837b5eb41958cc714d3438ec5ad\",\"devName\":\"测试专用门禁\",\"identityCardUri\":null,\"receiveTime\":\"2025-08-07T11:01:39.130+08:00\",\"jobNo\":null,\"studentId\":null,\"certNo\":null},{\"eventId\":\"72e47f945277c1090f26b2bcabc44406\",\"eventName\":\"acs.acs.eventType.infoRemoteCloseDoor\",\"eventTime\":\"2025-08-07T11:01:37+08:00\",\"personId\":null,\"cardNo\":null,\"personName\":null,\"orgIndexCode\":null,\"orgName\":null,\"doorName\":\"测试专用门禁_门_1\",\"doorIndexCode\":\"51de8916ef77402895a83a90fd08fb7b\",\"doorRegionIndexCode\":\"b3bc65c498264e649ef0338590f63c10\",\"picUri\":null,\"svrIndexCode\":null,\"eventType\":199171,\"inAndOutType\":-1,\"readerDevIndexCode\":null,\"readerDevName\":null,\"devIndexCode\":\"2a659837b5eb41958cc714d3438ec5ad\",\"devName\":\"测试专用门禁\",\"identityCardUri\":null,\"receiveTime\":\"2025-08-07T11:01:39.130+08:00\",\"jobNo\":null,\"studentId\":null,\"certNo\":null},{\"eventId\":\"72214e56ba8ad1099d7971839af0ac97\",\"eventName\":\"acs.acs.eventType.infoCloseDoor\",\"eventTime\":\"2025-08-07T11:01:35+08:00\",\"personId\":null,\"cardNo\":null,\"personName\":null,\"orgIndexCode\":null,\"orgName\":null,\"doorName\":\"测试专用门禁_门_1\",\"doorIndexCode\":\"51de8916ef77402895a83a90fd08fb7b\",\"doorRegionIndexCode\":\"b3bc65c498264e649ef0338590f63c10\",\"picUri\":null,\"svrIndexCode\":null,\"eventType\":199169,\"inAndOutType\":-1,\"readerDevIndexCode\":null,\"readerDevName\":null,\"devIndexCode\":\"2a659837b5eb41958cc714d3438ec5ad\",\"devName\":\"测试专用门禁\",\"identityCardUri\":null,\"receiveTime\":\"2025-08-07T11:01:36.953+08:00\",\"jobNo\":null,\"studentId\":null,\"certNo\":null},{\"eventId\":\"e96093af9788519fda2c29ba71ef4c52\",\"eventName\":\"acs.acs.eventType.infoDoorLockClose\",\"eventTime\":\"2025-08-07T11:01:35+08:00\",\"personId\":null,\"cardNo\":null,\"personName\":null,\"orgIndexCode\":null,\"orgName\":null,\"doorName\":\"测试专用门禁_门_1\",\"doorIndexCode\":\"51de8916ef77402895a83a90fd08fb7b\",\"doorRegionIndexCode\":\"b3bc65c498264e649ef0338590f63c10\",\"picUri\":null,\"svrIndexCode\":null,\"eventType\":199942,\"inAndOutType\":-1,\"readerDevIndexCode\":null,\"readerDevName\":null,\"devIndexCode\":\"2a659837b5eb41958cc714d3438ec5ad\",\"devName\":\"测试专用门禁\",\"identityCardUri\":null,\"receiveTime\":\"2025-08-07T11:01:36.953+08:00\",\"jobNo\":null,\"studentId\":null,\"certNo\":null},{\"eventId\":\"704bd8854ff58a989f7f77b417b63bd9\",\"eventName\":\"acs.acs.eventType.infoOpenDoor\",\"eventTime\":\"2025-08-07T11:01:30+08:00\",\"personId\":null,\"cardNo\":null,\"personName\":null,\"orgIndexCode\":null,\"orgName\":null,\"doorName\":\"测试专用门禁_门_1\",\"doorIndexCode\":\"51de8916ef77402895a83a90fd08fb7b\",\"doorRegionIndexCode\":\"b3bc65c498264e649ef0338590f63c10\",\"picUri\":null,\"svrIndexCode\":null,\"eventType\":198913,\"inAndOutType\":-1,\"readerDevIndexCode\":null,\"readerDevName\":null,\"devIndexCode\":\"2a659837b5eb41958cc714d3438ec5ad\",\"devName\":\"测试专用门禁\",\"identityCardUri\":null,\"receiveTime\":\"2025-08-07T11:01:32.488+08:00\",\"jobNo\":null,\"studentId\":null,\"certNo\":null},{\"eventId\":\"77a12882a0e4666f4137a99eedbc09a8\",\"eventName\":\"acs.acs.eventType.infoRemoteOpenDoor\",\"eventTime\":\"2025-08-07T11:01:30+08:00\",\"personId\":null,\"cardNo\":null,\"personName\":null,\"orgIndexCode\":null,\"orgName\":null,\"doorName\":\"测试专用门禁_门_1\",\"doorIndexCode\":\"51de8916ef77402895a83a90fd08fb7b\",\"doorRegionIndexCode\":\"b3bc65c498264e649ef0338590f63c10\",\"picUri\":null,\"svrIndexCode\":null,\"eventType\":198919,\"inAndOutType\":-1,\"readerDevIndexCode\":null,\"readerDevName\":null,\"devIndexCode\":\"2a659837b5eb41958cc714d3438ec5ad\",\"devName\":\"测试专用门禁\",\"identityCardUri\":null,\"receiveTime\":\"2025-08-07T11:01:32.487+08:00\",\"jobNo\":null,\"studentId\":null,\"certNo\":null},{\"eventId\":\"aed488ce620035f30d7bbbcdef309eaa\",\"eventName\":\"acs.acs.eventType.infoDoorLockOpen\",\"eventTime\":\"2025-08-07T11:01:30+08:00\",\"personId\":null,\"cardNo\":null,\"personName\":null,\"orgIndexCode\":null,\"orgName\":null,\"doorName\":\"测试专用门禁_门_1\",\"doorIndexCode\":\"51de8916ef77402895a83a90fd08fb7b\",\"doorRegionIndexCode\":\"b3bc65c498264e649ef0338590f63c10\",\"picUri\":null,\"svrIndexCode\":null,\"eventType\":199941,\"inAndOutType\":-1,\"readerDevIndexCode\":null,\"readerDevName\":null,\"devIndexCode\":\"2a659837b5eb41958cc714d3438ec5ad\",\"devName\":\"测试专用门禁\",\"identityCardUri\":null,\"receiveTime\":\"2025-08-07T11:01:32.488+08:00\",\"jobNo\":null,\"studentId\":null,\"certNo\":null},{\"eventId\":\"9ec2333b3b237ecb76cdb4acd9ce5322\",\"eventName\":\"acs.acs.eventType.infoCloseDoor\",\"eventTime\":\"2025-08-05T12:35:10+08:00\",\"personId\":null,\"cardNo\":null,\"personName\":null,\"orgIndexCode\":null,\"orgName\":null,\"doorName\":\"测试专用门禁_门_1\",\"doorIndexCode\":\"51de8916ef77402895a83a90fd08fb7b\",\"doorRegionIndexCode\":\"b3bc65c498264e649ef0338590f63c10\",\"picUri\":null,\"svrIndexCode\":null,\"eventType\":199169,\"inAndOutType\":-1,\"readerDevIndexCode\":null,\"readerDevName\":null,\"devIndexCode\":\"2a659837b5eb41958cc714d3438ec5ad\",\"devName\":\"测试专用门禁\",\"identityCardUri\":null,\"receiveTime\":\"2025-08-05T12:35:11.813+08:00\",\"jobNo\":null,\"studentId\":null,\"certNo\":null},{\"eventId\":\"d9a998d9518ab4d87d4f21a158465d1d\",\"eventName\":\"acs.acs.eventType.infoDoorLockClose\",\"eventTime\":\"2025-08-05T12:35:09+08:00\",\"personId\":null,\"cardNo\":null,\"personName\":null,\"orgIndexCode\":null,\"orgName\":null,\"doorName\":\"测试专用门禁_门_1\",\"doorIndexCode\":\"51de8916ef77402895a83a90fd08fb7b\",\"doorRegionIndexCode\":\"b3bc65c498264e649ef0338590f63c10\",\"picUri\":null,\"svrIndexCode\":null,\"eventType\":199942,\"inAndOutType\":-1,\"readerDevIndexCode\":null,\"readerDevName\":null,\"devIndexCode\":\"2a659837b5eb41958cc714d3438ec5ad\",\"devName\":\"测试专用门禁\",\"identityCardUri\":null,\"receiveTime\":\"2025-08-05T12:35:11.812+08:00\",\"jobNo\":null,\"studentId\":null,\"certNo\":null},{\"eventId\":\"21c72b8f754db96142079e80ab92d704\",\"eventName\":\"acs.acs.eventType.successCard\",\"eventTime\":\"2025-08-05T12:35:04+08:00\",\"personId\":\"20250723d20u8pv4\",\"cardNo\":\"1633214462\",\"personName\":\"高新兴测试\",\"orgIndexCode\":\"f62052c573d7434ebd09b65e4f4e82df\",\"orgName\":\"默认组织/第三看守所/人员\",\"doorName\":\"测试专用门禁_门_1\",\"doorIndexCode\":\"51de8916ef77402895a83a90fd08fb7b\",\"doorRegionIndexCode\":\"b3bc65c498264e649ef0338590f63c10\",\"picUri\":null,\"svrIndexCode\":null,\"eventType\":198914,\"inAndOutType\":1,\"readerDevIndexCode\":\"5feae9ccf0e7440b90de534edc8864a8\",\"readerDevName\":\"读卡器_1\",\"devIndexCode\":\"2a659837b5eb41958cc714d3438ec5ad\",\"devName\":\"测试专用门禁\",\"identityCardUri\":null,\"receiveTime\":\"2025-08-05T12:35:05.421+08:00\",\"jobNo\":\"20250723d20u8pv4\",\"studentId\":null,\"certNo\":\"20250723d20u8pv4\"}],\"total\":127},\"success\":true}\n";
    }

    public static String getControlData(String doorIndexCode) {
        return "{\"code\":\"0\",\"msg\":\"success\",\"data\":[{\"doorIndexCode\":\"" + doorIndexCode + "\",\"controlResultCode\":0,\"controlResultDesc\":\"success\"}]}";
    }

    public static String getStatesData(Map<String, Object> paramMap) {
        Map<String, Object> data = new HashMap<>(0);
        data.put("code", "0");
        data.put("msg", "success");
        Map<String, Object> dataList = new HashMap<>(0);
        List<Map<String, Object>> list = new ArrayList<>();
        List<String> doorIndexCodes = (List<String>) paramMap.get("doorIndexCodes");
        for (int i = 0; i < doorIndexCodes.size(); i++) {
            Map<String, Object> doorState = new HashMap<>(0);
            doorState.put("doorIndexCode", doorIndexCodes.get(i));
            int doorStateValue = i % 5;
            doorState.put("doorState", doorStateValue);
            list.add(doorState);
        }
        dataList.put("authDoorList", list);
        data.put("data", dataList);
        return JSON.toJSONString(data);
    }

    // 测试方法
    public static void main(String[] args) {
        List<Map<String, Object>> resources = generateThirdPrisonDoorResourcesAsMap();
        System.out.println("生成的Map数据数量：" + resources.size());
        // 打印第一条数据的关键字段
        if (!resources.isEmpty()) {
            Map<String, Object> first = resources.get(0);
            System.out.println("第一条数据关键信息：");
            System.out.println("name: " + first.get("name"));
            System.out.println("regionPathName: " + first.get("regionPathName"));
            System.out.println("regionIndexCode: " + first.get("regionIndexCode"));
        }
    }
}