#!/bin/bash

# 测试rs-acp配置文件读取功能

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 从YAML文件中提取配置值
extract_config_value() {
    local file_path="$1"
    local key_path="$2"
    local default_value="$3"
    
    if [ ! -f "$file_path" ]; then
        echo "$default_value"
        return
    fi
    
    # 使用awk提取YAML配置值
    local value=$(awk -v key="$key_path" '
    BEGIN { 
        split(key, keys, ".")
        depth = 0
        target_depth = length(keys)
        found = 0
    }
    {
        # 计算当前行的缩进深度
        match($0, /^[ ]*/)
        current_depth = RLENGTH / 2
        
        # 移除前导空格和注释
        gsub(/^[ ]*/, "")
        gsub(/#.*$/, "")
        if ($0 == "") next
        
        # 检查是否匹配当前层级的键
        if (current_depth < target_depth && match($0, /^[^:]+:/)) {
            key_name = substr($0, 1, RSTART + RLENGTH - 2)
            gsub(/:$/, "", key_name)
            
            if (current_depth + 1 <= target_depth && key_name == keys[current_depth + 1]) {
                depth = current_depth + 1
                if (depth == target_depth) {
                    # 找到目标键，提取值
                    if (match($0, /: */)) {
                        value = substr($0, RSTART + RLENGTH)
                        gsub(/^[ ]*/, "", value)
                        gsub(/[ ]*$/, "", value)
                        if (value != "") {
                            print value
                            found = 1
                            exit
                        }
                    }
                }
            } else if (current_depth < depth) {
                depth = current_depth
            }
        }
    }' "$file_path")
    
    if [ -z "$value" ]; then
        echo "$default_value"
    else
        echo "$value"
    fi
}

# 安全提取配置值的函数
safe_extract() {
    local value="$1"
    # 移除前后空格、制表符、换行符等
    echo "$value" | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//' | tr -d '\r\n'
}

# 测试rs-acp配置文件读取
test_acp_config() {
    local acp_config_file="rs-acp/config/application-conf.yml"
    
    echo "=========================================="
    echo "  测试rs-acp配置文件读取"
    echo "=========================================="
    echo ""
    
    # 检查文件是否存在
    if [ ! -f "$acp_config_file" ]; then
        print_error "配置文件不存在: $acp_config_file"
        echo ""
        print_info "当前目录: $(pwd)"
        print_info "查找rs-acp目录:"
        if [ -d "rs-acp" ]; then
            echo "  ✓ rs-acp 目录存在"
            if [ -d "rs-acp/config" ]; then
                echo "  ✓ rs-acp/config 目录存在"
                ls -la rs-acp/config/
            else
                echo "  ✗ rs-acp/config 目录不存在"
            fi
        else
            echo "  ✗ rs-acp 目录不存在"
        fi
        return 1
    fi
    
    print_success "配置文件存在: $acp_config_file"
    echo ""
    
    # 测试配置提取
    print_info "测试配置提取:"
    
    # 服务器配置
    local server_ip=$(extract_config_value "$acp_config_file" "conf.server.ip" "未找到")
    echo "  服务器IP: $(safe_extract "$server_ip")"
    
    # 系统配置
    local ignores=$(awk '/matchers:/,/debug:/ {if(/ignores:/) print}' "$acp_config_file" | head -1 | sed 's/.*ignores: *//' | sed 's/ *$//')
    echo "  忽略路径: $(safe_extract "$ignores")"
    
    # 数据库配置
    local pg_url=$(grep -E "^\s*url:.*postgresql" "$acp_config_file" | head -1 | sed 's/.*url: *//' | sed 's/ *$//')
    echo "  PostgreSQL URL: $(safe_extract "$pg_url")"
    
    local mysql_url=$(grep -E "^\s*url:.*mysql" "$acp_config_file" | head -1 | sed 's/.*url: *//' | sed 's/ *$//')
    echo "  MySQL URL: $(safe_extract "$mysql_url")"
    
    # Redis配置
    local redis_host=$(awk '/redis:/,/rabbitmq:/ {if(/host:/) print}' "$acp_config_file" | head -1 | sed 's/.*host: *//' | sed 's/ *$//')
    echo "  Redis 主机: $(safe_extract "$redis_host")"
    
    echo ""
    print_success "配置提取测试完成！"
}

# 主函数
main() {
    test_acp_config
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
