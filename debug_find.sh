#!/bin/bash

# 调试脚本：查看实际找到的文件

echo "=========================================="
echo "  调试：查找 application-conf.yml 文件"
echo "=========================================="
echo ""

echo "1. 使用 find 命令查找所有 application-conf.yml 文件："
echo "命令: find . -name \"application-conf.yml\" -type f"
echo ""

find . -name "application-conf.yml" -type f | while read -r file; do
    echo "找到文件: $file"
done

echo ""
echo "2. 统计文件数量："
file_count=$(find . -name "application-conf.yml" -type f | wc -l)
echo "总共找到: $file_count 个文件"

echo ""
echo "3. 详细信息："
find . -name "application-conf.yml" -type f -exec ls -la {} \;

echo ""
echo "4. 检查是否有隐藏文件或特殊字符："
find . -name "*application-conf.yml*" -type f

echo ""
echo "5. 检查目录结构："
echo "当前目录: $(pwd)"
echo "子目录列表:"
ls -la | grep "^d"

echo ""
echo "6. 检查 rs-acp 和 rs-dam 目录："
if [ -d "rs-acp" ]; then
    echo "rs-acp 目录存在"
    if [ -d "rs-acp/config" ]; then
        echo "rs-acp/config 目录存在"
        ls -la rs-acp/config/
    else
        echo "rs-acp/config 目录不存在"
    fi
else
    echo "rs-acp 目录不存在"
fi

if [ -d "rs-dam" ]; then
    echo "rs-dam 目录存在"
    if [ -d "rs-dam/config" ]; then
        echo "rs-dam/config 目录存在"
        ls -la rs-dam/config/
    else
        echo "rs-dam/config 目录不存在"
    fi
else
    echo "rs-dam 目录不存在"
fi

echo ""
echo "7. 使用数组测试："
files=($(find . -name "application-conf.yml" -type f 2>/dev/null))
echo "数组长度: ${#files[@]}"
echo "数组内容:"
for i in "${!files[@]}"; do
    echo "  [$i]: '${files[i]}'"
done
