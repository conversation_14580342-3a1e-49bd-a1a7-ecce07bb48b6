# 模块化配置参数使用演示

## 功能概述

脚本现在支持通过命令行参数来选择性配置特定的模块，让配置更新变得更加精确和高效。

## 可用的配置模块

| 模块名称 | 说明 | 包含的配置项 |
|---------|------|-------------|
| `server` | 服务器配置 | IP地址、ACP端口 |
| `system` | 系统配置 | 忽略路径配置 |
| `database` | 数据库配置 | PostgreSQL、MySQL连接信息 |
| `middleware` | 中间件配置 | Redis、MongoDB、RabbitMQ |
| `nacos` | Nacos配置 | 端口、用户名、密码、命名空间 |
| `storage` | 文件存储配置 | 端点、密钥、存储桶 |
| `business` | 业务服务配置 | BSP、BPM基础URL |
| `xxljob` | XXL-Job配置 | 管理地址、用户名、密码、执行器 |
| `qysh` | 清研手环配置 | 服务器IP、端口、用户名、密码 |
| `haikang` | 海康威视配置 | 区域代码、组织代码、主机、密钥 |

## 使用方法

### 1. 查看帮助信息

```bash
bash update_application_conf.sh --help
# 或
bash update_application_conf.sh -h
```

### 2. 列出所有可用模块

```bash
bash update_application_conf.sh --list
# 或
bash update_application_conf.sh -l
```

### 3. 配置所有模块（默认行为）

```bash
bash update_application_conf.sh
```

### 4. 配置单个模块

```bash
# 只配置数据库
bash update_application_conf.sh database

# 只配置服务器
bash update_application_conf.sh server

# 只配置中间件
bash update_application_conf.sh middleware
```

### 5. 配置多个模块

```bash
# 配置服务器和数据库
bash update_application_conf.sh server database

# 配置中间件和存储
bash update_application_conf.sh middleware storage

# 配置设备相关配置
bash update_application_conf.sh qysh haikang
```

## 使用场景示例

### 场景1: 环境迁移 - 只更新服务器配置

当从测试环境迁移到生产环境时，通常只需要更新IP地址：

```bash
bash update_application_conf.sh server
```

输出：
```
将配置以下模块: server

=== 服务器配置 ===
服务器IP地址 [当前: *************]: **********
ACP服务端口 [当前: 9100]: 

确认继续更新？(y/N): y
```

### 场景2: 数据库迁移 - 只更新数据库配置

当需要切换数据库服务器时：

```bash
bash update_application_conf.sh database
```

输出：
```
将配置以下模块: database

=== 数据库配置 ===
PostgreSQL连接URL [当前: jdbc:postgresql://*************:5432/rs_v1]: jdbc:postgresql://**********:5432/rs_prod
PostgreSQL用户名 [当前: postgres]: 
PostgreSQL密码 [当前: Go@123456]: NewProdPassword
MySQL连接URL [当前: ****************************************]: 
MySQL用户名 [当前: root]: 
MySQL密码 [当前: sundun_bsp]: 
```

### 场景3: 中间件配置调整

当需要调整Redis、MongoDB等中间件配置时：

```bash
bash update_application_conf.sh middleware
```

输出：
```
将配置以下模块: middleware

=== 中间件配置 ===
--- Redis配置 ---
Redis服务器地址 [当前: *************]: **********
Redis端口 [当前: 6399]: 6379
Redis密码 [当前: redisbsp]: 
Redis数据库编号 [当前: 3]: 0

--- MongoDB配置 ---
MongoDB连接URI [当前: mongodb://*************:27111/bsp]: 

--- RabbitMQ配置 ---
RabbitMQ地址 [当前: *************:5682]: 
RabbitMQ用户名 [当前: root]: 
RabbitMQ密码 [当前: sundun_bsp]: 
```

### 场景4: 设备配置更新

当需要配置清研手环和海康威视设备时：

```bash
bash update_application_conf.sh qysh haikang
```

输出：
```
将配置以下模块: qysh haikang

=== 清研手环(QYSH)配置 ===
清研手环服务器IP [当前: *************]: **********
清研手环WebSocket端口 [当前: 48300]: 
清研手环Web端口 [当前: 8180]: 
清研手环用户名 [当前: admin]: 
清研手环密码 [当前: #LocalSense]: 

=== 海康威视(Haikang)配置 ===
海康威视区域代码 [当前: root00000000]: 
海康威视组织代码 [当前: 110000113]: 
海康威视主机地址 [当前: 127.0.0.1]: **********
海康威视应用密钥 [当前: xxxx]: real_app_key
海康威视应用秘钥 [当前: xxxx]: real_app_secret
```

### 场景5: 基础设施配置

当需要配置服务器、中间件和Nacos时：

```bash
bash update_application_conf.sh server middleware nacos
```

## 优势特性

### 1. 🎯 精确配置
- 只显示和配置选定的模块
- 避免不必要的配置项干扰
- 提高配置效率

### 2. 🚀 快速操作
- 减少用户输入时间
- 专注于特定的配置需求
- 适合不同的使用场景

### 3. 🔒 安全可控
- 降低误操作风险
- 只修改相关的配置项
- 保持其他配置的稳定性

### 4. 📊 清晰反馈
- 明确显示将要配置的模块
- 详细的操作日志
- 清晰的统计信息

## 错误处理

### 无效的模块名称
```bash
bash update_application_conf.sh invalid_module
```

输出：
```
[ERROR] 未知参数: invalid_module

用法: update_application_conf.sh [选项] [配置模块]
...
```

### 帮助信息
```bash
bash update_application_conf.sh --help
```

输出完整的使用说明和可用模块列表。

## 最佳实践

1. **环境迁移**: 使用 `server` 模块快速更新IP配置
2. **数据库切换**: 使用 `database` 模块专门处理数据库配置
3. **中间件调优**: 使用 `middleware` 模块调整缓存和消息队列
4. **设备集成**: 使用 `qysh` 或 `haikang` 模块配置特定设备
5. **批量部署**: 组合多个模块进行批量配置

这种模块化的设计让配置管理变得更加灵活和高效！
