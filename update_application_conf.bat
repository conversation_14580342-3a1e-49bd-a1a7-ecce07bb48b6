@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 批量更新application-conf.yml配置文件脚本 (Windows版本)
:: 作者: AI Assistant
:: 用途: 遍历当前目录及子目录，找到所有application-conf.yml文件并更新配置

echo ==========================================
echo   application-conf.yml 批量更新工具
echo ==========================================
echo.
echo 此脚本将帮助您批量更新项目中所有的application-conf.yml配置文件
echo.
echo 支持更新的配置项：
echo 1. 服务器IP地址
echo 2. 服务端口
echo 3. PostgreSQL数据库配置
echo 4. MySQL数据库配置
echo 5. Redis配置
echo 6. MongoDB配置
echo 7. RabbitMQ配置
echo 8. Nacos配置
echo 9. 文件存储配置
echo 10. 其他第三方服务配置
echo.

:: 查找所有application-conf.yml文件
echo [INFO] 正在搜索application-conf.yml文件...
set file_count=0
for /r %%f in (application-conf.yml) do (
    if exist "%%f" (
        set /a file_count+=1
        echo   !file_count!. %%f
        set "config_file_!file_count!=%%f"
    )
)

if !file_count! equ 0 (
    echo [WARNING] 未找到任何application-conf.yml文件
    pause
    exit /b 1
)

echo.
echo [SUCCESS] 找到 !file_count! 个配置文件
echo.

:: 获取用户输入的配置值
echo 请输入需要更新的配置值（直接回车跳过该项）：
echo.

echo === 服务器配置 ===
set /p SERVER_IP="服务器IP地址 [当前: *************]: "
set /p ACP_PORT="ACP服务端口 [当前: 9100]: "
echo.

echo === 数据库配置 ===
set /p PG_HOST="PostgreSQL服务器地址 [当前: *************:5432]: "
set /p PG_DATABASE="PostgreSQL数据库名 [当前: rs_v1]: "
set /p PG_USERNAME="PostgreSQL用户名 [当前: postgres]: "
set /p PG_PASSWORD="PostgreSQL密码 [当前: Go@123456]: "
echo.

set /p MYSQL_HOST="MySQL服务器地址 [当前: *************:3336]: "
set /p MYSQL_DATABASE="MySQL数据库名 [当前: bsp_v1.1]: "
set /p MYSQL_USERNAME="MySQL用户名 [当前: root]: "
set /p MYSQL_PASSWORD="MySQL密码 [当前: sundun_bsp]: "
echo.

echo === Redis配置 ===
set /p REDIS_HOST="Redis服务器地址 [当前: *************]: "
set /p REDIS_PORT="Redis端口 [当前: 6399]: "
set /p REDIS_PASSWORD="Redis密码 [当前: redisbsp]: "
set /p REDIS_DATABASE="Redis数据库编号 [当前: 3]: "
echo.

echo === MongoDB配置 ===
set /p MONGODB_URI="MongoDB连接URI [当前: mongodb://*************:27111/bsp]: "
echo.

echo === RabbitMQ配置 ===
set /p RABBITMQ_ADDRESS="RabbitMQ地址 [当前: *************:5682]: "
set /p RABBITMQ_USERNAME="RabbitMQ用户名 [当前: root]: "
set /p RABBITMQ_PASSWORD="RabbitMQ密码 [当前: sundun_bsp]: "
echo.

echo === Nacos配置 ===
set /p NACOS_PORT="Nacos端口 [当前: 8848]: "
set /p NACOS_USERNAME="Nacos用户名 [当前: nacos]: "
set /p NACOS_PASSWORD="Nacos密码 [当前: nacos@gxx]: "
set /p NACOS_NAMESPACE="Nacos命名空间 [当前: rs]: "
echo.

echo === 文件存储配置 ===
set /p STORAGE_ENDPOINT="文件存储端点 [当前: http://*************:9010]: "
set /p STORAGE_ACCESS_KEY="文件存储访问密钥 [当前: admin]: "
set /p STORAGE_SECRET_KEY="文件存储秘密密钥 [当前: admin123456]: "
set /p STORAGE_BUCKET="存储桶名称 [当前: ihc]: "
echo.

echo === BSP/BPM配置 ===
set /p BSP_BASE_URL="BSP基础URL [当前: http://*************:1910]: "
set /p BPM_BASE_URL="BPM基础URL [当前: http://*************:1911]: "
echo.

echo === XXL-Job配置 ===
set /p XXL_EXECUTOR_PORT="XXL-Job执行器端口 [当前: 9101]: "
set /p XXL_ADMIN_PASSWORD="XXL-Job管理员密码 [当前: xxlbsp]: "
echo.

:: 确认更新
echo ==========================================
echo [WARNING] 即将更新 !file_count! 个配置文件
echo 文件列表:
for /l %%i in (1,1,!file_count!) do (
    echo   - !config_file_%%i!
)
echo.
set /p confirm="确认继续更新？(y/N): "

if /i not "!confirm!"=="y" (
    echo [INFO] 操作已取消
    pause
    exit /b 0
)

:: 执行更新
echo.
echo [INFO] 开始批量更新配置文件...

for /l %%i in (1,1,!file_count!) do (
    set "current_file=!config_file_%%i!"
    echo [INFO] 正在更新文件: !current_file!
    
    :: 备份原文件
    for /f "tokens=1-6 delims=/:. " %%a in ("%date% %time%") do (
        set backup_suffix=%%c%%a%%b_%%d%%e%%f
    )
    set "backup_file=!current_file!.backup.!backup_suffix!"
    copy "!current_file!" "!backup_file!" >nul
    echo [INFO] 已备份文件: !backup_file!
    
    :: 创建PowerShell脚本来进行文本替换
    echo $content = Get-Content "!current_file!" -Encoding UTF8 > temp_replace.ps1
    
    :: 服务器IP替换
    if not "!SERVER_IP!"=="" (
        echo $content = $content -replace "ip: 192\.168\.3\.251", "ip: !SERVER_IP!" >> temp_replace.ps1
        echo $content = $content -replace "//192\.168\.3\.251", "//!SERVER_IP!" >> temp_replace.ps1
        echo $content = $content -replace "http://192\.168\.3\.251", "http://!SERVER_IP!" >> temp_replace.ps1
        echo $content = $content -replace "mongodb://192\.168\.3\.251", "mongodb://!SERVER_IP!" >> temp_replace.ps1
        echo $content = $content -replace "192\.168\.3\.251:5682", "!SERVER_IP!:5682" >> temp_replace.ps1
    )
    
    :: ACP端口替换
    if not "!ACP_PORT!"=="" (
        echo $content = $content -replace "acp: 9100", "acp: !ACP_PORT!" >> temp_replace.ps1
    )
    
    :: PostgreSQL配置替换
    if not "!PG_HOST!"=="" (
        echo $content = $content -replace "***************************************", "jdbc:postgresql://!PG_HOST!" >> temp_replace.ps1
    )
    if not "!PG_DATABASE!"=="" (
        echo $content = $content -replace "5432/rs_v1", "5432/!PG_DATABASE!" >> temp_replace.ps1
    )
    
    :: Redis配置替换
    if not "!REDIS_HOST!"=="" (
        echo $content = $content -replace "host: 192\.168\.3\.251", "host: !REDIS_HOST!" >> temp_replace.ps1
    )
    if not "!REDIS_PORT!"=="" (
        echo $content = $content -replace "port: 6399", "port: !REDIS_PORT!" >> temp_replace.ps1
    )
    
    :: 保存文件
    echo $content ^| Set-Content "!current_file!" -Encoding UTF8 >> temp_replace.ps1
    
    :: 执行PowerShell脚本
    powershell -ExecutionPolicy Bypass -File temp_replace.ps1
    
    :: 清理临时文件
    del temp_replace.ps1
    
    echo [SUCCESS] 文件更新完成: !current_file!
)

echo.
echo [SUCCESS] 所有配置文件更新完成！
echo [INFO] 备份文件已保存，如需恢复可使用备份文件
echo.
echo ==========================================
echo 更新摘要:
echo - 更新文件数量: !file_count!
echo - 备份文件位置: 原文件同目录下的 .backup.* 文件
echo ==========================================

pause
