# 清研手环(QYSH)配置更新演示

## 配置项说明

清研手环配置位于 `application-conf.yml` 文件的 `device.qysh` 节点下：

```yaml
# 清研手环配置
device:
  qysh:
    serverIp: *************          # 服务器IP地址
    websocketPort: 48300             # WebSocket端口
    webPort: 8180                    # Web服务端口
    username: admin                  # 登录用户名
    password: "#LocalSense"          # 登录密码
    positionChangeThresholdX: 5      # X轴位置变化阈值
    positionChangeThresholdY: 5      # Y轴位置变化阈值
    positionChangeThresholdZ: 2.5    # Z轴位置变化阈值
```

## 脚本支持的配置项

脚本现在支持动态更新以下清研手环配置项：

1. **serverIp**: 清研手环服务器IP地址
2. **websocketPort**: WebSocket通信端口
3. **webPort**: Web服务端口
4. **username**: 登录用户名
5. **password**: 登录密码

> **注意**: 位置变化阈值配置（positionChangeThresholdX/Y/Z）暂不支持动态更新，因为这些通常是固定的业务参数。

## 使用示例

### 1. 运行脚本
```bash
bash update_application_conf.sh
```

### 2. 配置输入示例
```
=== 清研手环(QYSH)配置 ===
清研手环服务器IP [当前: *************]: **********
清研手环WebSocket端口 [当前: 48300]: 48301
清研手环Web端口 [当前: 8180]: 8181
清研手环用户名 [当前: admin]: qysh_admin
清研手环密码 [当前: #LocalSense]: NewSecurePassword123
```

### 3. 更新过程输出
```
[INFO] 正在更新文件: ./rs-module-acp/rs-module-acp-biz/src/main/resources/application-conf.yml
  ✓ 已更新清研手环服务器IP: ************* → **********
  ✓ 已更新清研手环WebSocket端口: 48300 → 48301
  ✓ 已更新清研手环Web端口: 8180 → 8181
  ✓ 已更新清研手环用户名: admin → qysh_admin
  ✓ 已更新清研手环密码: #LocalSense → NewSecurePassword123
```

### 4. 更新后的配置文件
```yaml
# 清研手环配置
device:
  qysh:
    serverIp: **********             # 已更新
    websocketPort: 48301             # 已更新
    webPort: 8181                    # 已更新
    username: qysh_admin             # 已更新
    password: "NewSecurePassword123" # 已更新
    positionChangeThresholdX: 5      # 保持不变
    positionChangeThresholdY: 5      # 保持不变
    positionChangeThresholdZ: 2.5    # 保持不变
```

## 特殊字符处理

脚本能够正确处理密码中的特殊字符，例如：
- `#LocalSense` (包含井号)
- `Pass@word123` (包含@符号)
- `"Complex#Pass$word"` (包含引号和特殊字符)

## 错误处理

如果某个配置文件中没有清研手环配置，脚本会安全跳过：

```
[INFO] 正在更新文件: ./some-module/src/main/resources/application-conf.yml
  ⚠ 跳过清研手环服务器IP: 在此文件中未找到该配置项
  ⚠ 跳过清研手环WebSocket端口: 在此文件中未找到该配置项
  ⚠ 跳过清研手环Web端口: 在此文件中未找到该配置项
  ⚠ 跳过清研手环用户名: 在此文件中未找到该配置项
  ⚠ 跳过清研手环密码: 在此文件中未找到该配置项

[INFO] 文件无需更新: ./some-module/src/main/resources/application-conf.yml
  - 跳过配置: 5 个配置项
  - 缺失配置项: 清研手环服务器IP 清研手环WebSocket端口 清研手环Web端口 清研手环用户名 清研手环密码
```

## 测试配置提取

可以使用测试脚本验证配置提取功能：

```bash
bash test_config_extraction.sh
```

测试输出示例：
```
=== 清研手环(QYSH)配置 ===
清研手环 服务器IP: *************
清研手环 WebSocket端口: 48300
清研手环 Web端口: 8180
清研手环 用户名: admin
清研手环 密码: #LocalSense
```

## 注意事项

1. **备份**: 脚本会自动备份原配置文件，格式为 `application-conf.yml.backup.YYYYMMDD_HHMMSS`

2. **权限**: 确保对配置文件有读写权限

3. **格式**: 脚本依赖YAML文件的正确格式和缩进

4. **密码安全**: 输入密码时注意周围环境，避免密码泄露

5. **验证**: 更新后建议验证应用程序能否正常连接清研手环设备
