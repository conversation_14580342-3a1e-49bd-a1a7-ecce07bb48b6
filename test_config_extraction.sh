#!/bin/bash

# 测试配置提取功能的脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 从YAML文件中提取配置值
extract_config_value() {
    local file_path="$1"
    local key_path="$2"
    local default_value="$3"

    if [ ! -f "$file_path" ]; then
        echo "$default_value"
        return
    fi

    # 使用awk提取YAML配置值
    local value=$(awk -v key="$key_path" '
    BEGIN {
        split(key, keys, ".")
        depth = 0
        target_depth = length(keys)
        found = 0
    }
    {
        # 计算当前行的缩进深度
        match($0, /^[ ]*/)
        current_depth = RLENGTH / 2

        # 移除前导空格和注释
        gsub(/^[ ]*/, "")
        gsub(/#.*$/, "")
        if ($0 == "") next

        # 检查是否匹配当前层级的键
        if (current_depth < target_depth && match($0, /^[^:]+:/)) {
            key_name = substr($0, 1, RSTART + RLENGTH - 2)
            gsub(/:$/, "", key_name)

            if (current_depth + 1 <= target_depth && key_name == keys[current_depth + 1]) {
                depth = current_depth + 1
                if (depth == target_depth) {
                    # 找到目标键，提取值
                    if (match($0, /: */)) {
                        value = substr($0, RSTART + RLENGTH)
                        gsub(/^[ ]*/, "", value)
                        gsub(/[ ]*$/, "", value)
                        if (value != "") {
                            print value
                            found = 1
                            exit
                        }
                    }
                }
            } else if (current_depth < depth) {
                depth = current_depth
            }
        }
    }' "$file_path")

    if [ -z "$value" ]; then
        echo "$default_value"
    else
        echo "$value"
    fi
}

# 测试配置提取
test_config_extraction() {
    local config_file="$1"

    if [ ! -f "$config_file" ]; then
        print_error "配置文件不存在: $config_file"
        return 1
    fi

    print_info "测试配置文件: $config_file"
    echo "=========================================="

    # 测试服务器配置
    echo "=== 服务器配置 ==="
    local server_ip=$(extract_config_value "$config_file" "conf.server.ip" "未找到")
    echo "服务器IP: $server_ip"
    echo ""

    # 测试系统配置
    echo "=== 系统配置 ==="
    local ignores=$(awk '/matchers:/,/debug:/ {if(/ignores:/) print}' "$config_file" | head -1 | sed 's/.*ignores: *//' | sed 's/ *$//')
    echo "忽略路径配置: ${ignores:-未找到}"
    echo ""

    # 测试数据库配置
    echo "=== 数据库配置 ==="
    local pg_url=$(grep -E "^\s*url:.*postgresql" "$config_file" | head -1 | sed 's/.*url: *//' | sed 's/ *$//')
    local pg_username=$(awk '/postgresql/,/bsp:/ {if(/username:/ && !/nacos/) print}' "$config_file" | head -1 | sed 's/.*username: *//' | sed 's/ *$//')
    local pg_password=$(awk '/postgresql/,/bsp:/ {if(/password:/ && !/nacos/) print}' "$config_file" | head -1 | sed 's/.*password: *//' | sed 's/ *$//')
    echo "PostgreSQL URL: ${pg_url:-未找到}"
    echo "PostgreSQL 用户名: ${pg_username:-未找到}"
    echo "PostgreSQL 密码: ${pg_password:-未找到}"
    echo ""

    local mysql_url=$(grep -E "^\s*url:.*mysql" "$config_file" | head -1 | sed 's/.*url: *//' | sed 's/ *$//')
    local mysql_username=$(awk '/mysql/,/mongodb:/ {if(/username:/) print}' "$config_file" | head -1 | sed 's/.*username: *//' | sed 's/ *$//')
    local mysql_password=$(awk '/mysql/,/mongodb:/ {if(/password:/) print}' "$config_file" | head -1 | sed 's/.*password: *//' | sed 's/ *$//')
    echo "MySQL URL: ${mysql_url:-未找到}"
    echo "MySQL 用户名: ${mysql_username:-未找到}"
    echo "MySQL 密码: ${mysql_password:-未找到}"
    echo ""

    # 测试Redis配置
    echo "=== Redis配置 ==="
    local redis_host=$(awk '/redis:/,/rabbitmq:/ {if(/host:/) print}' "$config_file" | head -1 | sed 's/.*host: *//' | sed 's/ *$//')
    local redis_port=$(awk '/redis:/,/rabbitmq:/ {if(/port:/) print}' "$config_file" | head -1 | sed 's/.*port: *//' | sed 's/ *$//')
    local redis_password=$(awk '/redis:/,/rabbitmq:/ {if(/password:/) print}' "$config_file" | head -1 | sed 's/.*password: *//' | sed 's/ *$//')
    local redis_database=$(awk '/redis:/,/rabbitmq:/ {if(/database:/) print}' "$config_file" | head -1 | sed 's/.*database: *//' | sed 's/ *$//')
    echo "Redis 主机: ${redis_host:-未找到}"
    echo "Redis 端口: ${redis_port:-未找到}"
    echo "Redis 密码: ${redis_password:-未找到}"
    echo "Redis 数据库: ${redis_database:-未找到}"
    echo ""

    # 测试XXL-Job配置
    echo "=== XXL-Job配置 ==="
    local xxl_addresses=$(awk '/xxl:/,/---/ {if(/addresses:/) print}' "$config_file" | head -1 | sed 's/.*addresses: *//' | sed 's/ *$//')
    local xxl_admin_username=$(awk '/admin:/,/executor:/ {if(/username:/) print}' "$config_file" | head -1 | sed 's/.*username: *//' | sed 's/ *$//')
    local xxl_admin_password=$(awk '/admin:/,/executor:/ {if(/password:/) print}' "$config_file" | head -1 | sed 's/.*password: *//' | sed 's/ *$//')
    local xxl_executor_ip=$(awk '/executor:/,/haikang:/ {if(/ip:/) print}' "$config_file" | head -1 | sed 's/.*ip: *//' | sed 's/ *$//')
    local xxl_executor_port=$(awk '/executor:/,/haikang:/ {if(/port:/) print}' "$config_file" | head -1 | sed 's/.*port: *//' | sed 's/ *$//')
    echo "XXL-Job 管理地址: ${xxl_addresses:-未找到}"
    echo "XXL-Job 管理员用户名: ${xxl_admin_username:-未找到}"
    echo "XXL-Job 管理员密码: ${xxl_admin_password:-未找到}"
    echo "XXL-Job 执行器IP: ${xxl_executor_ip:-未找到}"
    echo "XXL-Job 执行器端口: ${xxl_executor_port:-未找到}"
    echo ""

    # 测试清研手环(qysh)配置
    echo "=== 清研手环(QYSH)配置 ==="
    local qysh_server_ip=$(awk '/qysh:/,/---/ {if(/serverIp:/) print}' "$config_file" | head -1 | sed 's/.*serverIp: *//' | sed 's/ *$//')
    local qysh_websocket_port=$(awk '/qysh:/,/---/ {if(/websocketPort:/) print}' "$config_file" | head -1 | sed 's/.*websocketPort: *//' | sed 's/ *$//')
    local qysh_web_port=$(awk '/qysh:/,/---/ {if(/webPort:/) print}' "$config_file" | head -1 | sed 's/.*webPort: *//' | sed 's/ *$//')
    local qysh_username=$(awk '/qysh:/,/---/ {if(/username:/) print}' "$config_file" | head -1 | sed 's/.*username: *//' | sed 's/ *$//')
    local qysh_password=$(awk '/qysh:/,/---/ {if(/password:/) print}' "$config_file" | head -1 | sed 's/.*password: *//' | sed 's/ *$//')
    echo "清研手环 服务器IP: ${qysh_server_ip:-未找到}"
    echo "清研手环 WebSocket端口: ${qysh_websocket_port:-未找到}"
    echo "清研手环 Web端口: ${qysh_web_port:-未找到}"
    echo "清研手环 用户名: ${qysh_username:-未找到}"
    echo "清研手环 密码: ${qysh_password:-未找到}"
    echo ""

    # 测试海康威视(haikang)配置
    echo "=== 海康威视(Haikang)配置 ==="
    local haikang_region=$(awk '/haikang:/,/ribbon:/ {if(/region:/) print}' "$config_file" | head -1 | sed 's/.*region: *//' | sed 's/ *$//')
    local haikang_orgcode=$(awk '/haikang:/,/ribbon:/ {if(/orgCode:/) print}' "$config_file" | head -1 | sed 's/.*orgCode: *//' | sed 's/ *$//')
    local haikang_host=$(awk '/config:/,/ribbon:/ {if(/host:/) print}' "$config_file" | head -1 | sed 's/.*host: *//' | sed 's/ *$//')
    local haikang_appkey=$(awk '/config:/,/ribbon:/ {if(/appKey:/) print}' "$config_file" | head -1 | sed 's/.*appKey: *//' | sed 's/ *$//')
    local haikang_appsecret=$(awk '/config:/,/ribbon:/ {if(/appSecret:/) print}' "$config_file" | head -1 | sed 's/.*appSecret: *//' | sed 's/ *$//')
    echo "海康威视 区域代码: ${haikang_region:-未找到}"
    echo "海康威视 组织代码: ${haikang_orgcode:-未找到}"
    echo "海康威视 主机地址: ${haikang_host:-未找到}"
    echo "海康威视 应用密钥: ${haikang_appkey:-未找到}"
    echo "海康威视 应用秘钥: ${haikang_appsecret:-未找到}"
    echo ""
}

# 主函数
main() {
    echo "=========================================="
    echo "  配置提取功能测试工具"
    echo "=========================================="
    echo ""

    # 查找所有配置文件
    print_info "搜索配置文件..."
    local config_files=($(find . -name "application-conf.yml" -type f 2>/dev/null))

    if [ ${#config_files[@]} -eq 0 ]; then
        print_warning "未找到任何application-conf.yml文件"
        exit 1
    fi

    print_success "找到 ${#config_files[@]} 个配置文件"
    echo ""

    # 测试每个配置文件
    for config_file in "${config_files[@]}"; do
        test_config_extraction "$config_file"
        echo ""
    done

    print_success "配置提取测试完成！"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
