package com.rs.module.ihc.service.approval;

import cn.hutool.core.map.MapBuilder;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.BpmApi;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.base.vo.SimpleApproveReqVO;
import com.rs.module.ihc.enums.ApplyStatusEnum;
import com.rs.module.ihc.enums.MedicineDeliveryApplyStatusEnum;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpStatus;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.lang.reflect.Field;

/**
 * @ClassName ApprovalServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/8/7 14:18
 * @Version 1.0
 */
public  interface ApprovalService<DO> extends IBaseService<DO> {
    public JSONObject startProcess(MapBuilder<String, Object> variables,  DO entity);
    public void approve(SimpleApproveReqVO approveReqVO);

}
