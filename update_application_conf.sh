#!/bin/bash

# 批量更新application-conf.yml配置文件脚本
# 作者: AI Assistant
# 用途: 遍历当前目录及子目录，找到所有application-conf.yml文件并更新配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示脚本使用说明
show_usage() {
    echo "=========================================="
    echo "  application-conf.yml 批量更新工具"
    echo "=========================================="
    echo ""
    echo "此脚本将帮助您批量更新项目中所有的application-conf.yml配置文件"
    echo ""
    echo "功能特性："
    echo "• 🎯 模块化配置 - 可选择性配置特定模块"
    echo "• 🔄 智能默认值 - 直接回车保持原值不变"
    echo "• 🛡️ 自动备份 - 更新前自动备份原文件"
    echo "• 📊 详细反馈 - 清晰显示更新状态和统计"
    echo "• 🔒 安全可靠 - 只修改用户指定的配置项"
    echo ""
}

# 显示可用的配置模块列表
show_modules() {
    echo "可用的配置模块："
    echo "  server      - 服务器配置 (IP地址、ACP端口)"
    echo "  system      - 系统配置 (忽略路径配置)"
    echo "  database    - 数据库配置 (PostgreSQL、MySQL)"
    echo "  middleware  - 中间件配置 (Redis、MongoDB、RabbitMQ)"
    echo "  nacos       - Nacos配置 (端口、用户名、密码、命名空间)"
    echo "  storage     - 文件存储配置 (端点、密钥、存储桶)"
    echo "  business    - 业务服务配置 (BSP、BPM基础URL)"
    echo "  xxljob      - XXL-Job配置 (管理地址、用户名、密码、执行器)"
    echo "  qysh        - 清研手环配置 (服务器IP、端口、用户名、密码)"
    echo "  haikang     - 海康威视配置 (区域代码、组织代码、主机、密钥)"
}

# 显示配置模块选择菜单
show_module_menu() {
    echo "=========================================="
    echo "  请选择要配置的模块"
    echo "=========================================="
    echo ""
    echo "0. 全部配置 (配置所有模块)"
    echo "1. 服务器配置 (server) - IP地址"
    echo "2. 系统配置 (system) - 忽略路径"
    echo "3. 数据库配置 (database) - PostgreSQL、MySQL"
    echo "4. 中间件配置 (middleware) - Redis、MongoDB、RabbitMQ"
    echo "5. Nacos配置 (nacos) - 服务注册与发现"
    echo "6. 文件存储配置 (storage) - 对象存储"
    echo "7. 业务服务配置 (business) - BSP、BPM"
    echo "8. XXL-Job配置 (xxljob) - 任务调度"
    echo "9. 清研手环配置 (qysh) - 设备集成"
    echo "10. 海康威视配置 (haikang) - 视频监控"
    echo ""
}

# 获取用户选择的配置模块
get_module_selection() {
    SELECTED_MODULES=()

    while true; do
        show_module_menu
        read -p "请输入要配置的模块编号 (多个编号用空格分隔，如: 1 3 4): " selection

        # 如果用户直接回车，默认选择全部配置
        if [ -z "$selection" ]; then
            selection="0"
        fi

        # 解析用户输入
        valid_selection=true
        for num in $selection; do
            case $num in
                0)
                    SELECTED_MODULES=("server" "system" "database" "middleware" "nacos" "storage" "business" "xxljob" "qysh" "haikang")
                    break
                    ;;
                1)
                    SELECTED_MODULES+=("server")
                    ;;
                2)
                    SELECTED_MODULES+=("system")
                    ;;
                3)
                    SELECTED_MODULES+=("database")
                    ;;
                4)
                    SELECTED_MODULES+=("middleware")
                    ;;
                5)
                    SELECTED_MODULES+=("nacos")
                    ;;
                6)
                    SELECTED_MODULES+=("storage")
                    ;;
                7)
                    SELECTED_MODULES+=("business")
                    ;;
                8)
                    SELECTED_MODULES+=("xxljob")
                    ;;
                9)
                    SELECTED_MODULES+=("qysh")
                    ;;
                10)
                    SELECTED_MODULES+=("haikang")
                    ;;
                *)
                    print_error "无效的选择: $num"
                    valid_selection=false
                    break
                    ;;
            esac
        done

        if [ "$valid_selection" = true ] && [ ${#SELECTED_MODULES[@]} -gt 0 ]; then
            break
        else
            echo ""
            print_warning "请输入有效的模块编号！"
            echo ""
            read -p "按回车键继续..."
            echo ""
        fi
    done

    # 去重
    SELECTED_MODULES=($(printf "%s\n" "${SELECTED_MODULES[@]}" | sort -u))

    echo ""
    if [[ " ${SELECTED_MODULES[*]} " =~ " server " ]] && [[ " ${SELECTED_MODULES[*]} " =~ " system " ]] && [[ " ${SELECTED_MODULES[*]} " =~ " database " ]] && [[ " ${SELECTED_MODULES[*]} " =~ " middleware " ]] && [[ " ${SELECTED_MODULES[*]} " =~ " nacos " ]] && [[ " ${SELECTED_MODULES[*]} " =~ " storage " ]] && [[ " ${SELECTED_MODULES[*]} " =~ " business " ]] && [[ " ${SELECTED_MODULES[*]} " =~ " xxljob " ]] && [[ " ${SELECTED_MODULES[*]} " =~ " qysh " ]] && [[ " ${SELECTED_MODULES[*]} " =~ " haikang " ]]; then
        print_info "已选择: 全部配置"
    else
        # 显示中文模块名称
        local module_names=()
        for module in "${SELECTED_MODULES[@]}"; do
            case $module in
                server) module_names+=("服务器配置") ;;
                system) module_names+=("系统配置") ;;
                database) module_names+=("数据库配置") ;;
                middleware) module_names+=("中间件配置") ;;
                nacos) module_names+=("Nacos配置") ;;
                storage) module_names+=("文件存储配置") ;;
                business) module_names+=("业务服务配置") ;;
                xxljob) module_names+=("XXL-Job配置") ;;
                qysh) module_names+=("清研手环配置") ;;
                haikang) module_names+=("海康威视配置") ;;
            esac
        done
        print_info "已选择: ${module_names[*]}"
    fi
    echo ""
}

# 检查模块是否被选中
is_module_selected() {
    local module="$1"
    for selected in "${SELECTED_MODULES[@]}"; do
        if [[ "$selected" == "$module" ]]; then
            return 0
        fi
    done
    return 1
}

# 备份文件函数
backup_file() {
    local file_path="$1"
    local backup_path="${file_path}.backup.$(date +%Y%m%d_%H%M%S)"
    cp "$file_path" "$backup_path"
    print_info "已备份文件: $backup_path"
}

# 查找所有application-conf.yml文件并显示信息
find_and_display_config_files() {
    print_info "正在搜索application-conf.yml文件..."

    # 使用更精确的搜索路径，优先搜索常见的配置目录
    local search_paths=(
        "./rs-acp/config"
        "./rs-bsp/config"
        "./rs-dam/config"
        "./rs-ihc/config"
        "./rs-pam/config"
        "./rs-rgf/config"
        "./rs-zhjg/config"
        "./*/config"
        "./*/src/main/resources"
        "./*/*/src/main/resources"
    )

    local files=()

    # 先尝试精确路径搜索
    for path in "${search_paths[@]}"; do
        if [ -d "${path%/*}" ]; then  # 检查父目录是否存在
            while IFS= read -r -d '' file; do
                files+=("$file")
            done < <(find "$path" -name "application-conf.yml" -type f -print0 2>/dev/null)
        fi
    done

    # 如果精确搜索没找到，则进行全局搜索
    if [ ${#files[@]} -eq 0 ]; then
        print_info "精确路径搜索未找到文件，进行全局搜索..."
        while IFS= read -r -d '' file; do
            files+=("$file")
        done < <(find . -name "application-conf.yml" -type f -print0 2>/dev/null)
    fi

    # 去重
    local unique_files=()
    for file in "${files[@]}"; do
        local found=false
        for unique_file in "${unique_files[@]}"; do
            if [ "$file" = "$unique_file" ]; then
                found=true
                break
            fi
        done
        if [ "$found" = false ]; then
            unique_files+=("$file")
        fi
    done

    if [ ${#unique_files[@]} -eq 0 ]; then
        print_warning "未找到任何application-conf.yml文件"
        print_info "请确认以下目录中是否存在配置文件："
        echo "  - ./rs-acp/config/application-conf.yml"
        echo "  - ./rs-dam/config/application-conf.yml"
        echo "  - 或其他模块的 src/main/resources/application-conf.yml"
        exit 1
    fi

    print_success "找到 ${#unique_files[@]} 个配置文件:"
    for i in "${!unique_files[@]}"; do
        echo "  $((i+1)). ${unique_files[i]}"
    done
    echo ""

    # 将文件列表存储到全局变量
    CONFIG_FILES=("${unique_files[@]}")
}

# 从YAML文件中提取配置值
extract_config_value() {
    local file_path="$1"
    local key_path="$2"
    local default_value="$3"

    if [ ! -f "$file_path" ]; then
        echo "$default_value"
        return
    fi

    # 使用awk提取YAML配置值
    local value=$(awk -v key="$key_path" '
    BEGIN {
        split(key, keys, ".")
        depth = 0
        target_depth = length(keys)
        found = 0
    }
    {
        # 计算当前行的缩进深度
        match($0, /^[ ]*/)
        current_depth = RLENGTH / 2

        # 移除前导空格和注释
        gsub(/^[ ]*/, "")
        gsub(/#.*$/, "")
        if ($0 == "") next

        # 检查是否匹配当前层级的键
        if (current_depth < target_depth && match($0, /^[^:]+:/)) {
            key_name = substr($0, 1, RSTART + RLENGTH - 2)
            gsub(/:$/, "", key_name)

            if (current_depth + 1 <= target_depth && key_name == keys[current_depth + 1]) {
                depth = current_depth + 1
                if (depth == target_depth) {
                    # 找到目标键，提取值
                    if (match($0, /: */)) {
                        value = substr($0, RSTART + RLENGTH)
                        gsub(/^[ ]*/, "", value)
                        gsub(/[ ]*$/, "", value)
                        if (value != "") {
                            print value
                            found = 1
                            exit
                        }
                    }
                }
            } else if (current_depth < depth) {
                depth = current_depth
            }
        }
    }' "$file_path")

    if [ -z "$value" ]; then
        echo "$default_value"
    else
        echo "$value"
    fi
}

# 分析rs-acp配置文件并提取当前值
analyze_first_config() {
    local acp_config_file="rs-acp/config/application-conf.yml"

    # 检查文件是否存在
    if [ ! -f "$acp_config_file" ]; then
        print_warning "未找到 rs-acp 配置文件: $acp_config_file"
        print_info "将使用默认值进行配置"
        # 设置默认值
        CURRENT_SERVER_IP="未找到"
        CURRENT_IGNORES="未找到"
        CURRENT_PG_URL="未找到"
        CURRENT_PG_USERNAME="未找到"
        CURRENT_PG_PASSWORD="未找到"
        CURRENT_MYSQL_URL="未找到"
        CURRENT_MYSQL_USERNAME="未找到"
        CURRENT_MYSQL_PASSWORD="未找到"
        CURRENT_REDIS_HOST="未找到"
        CURRENT_REDIS_PORT="未找到"
        CURRENT_REDIS_PASSWORD="未找到"
        CURRENT_REDIS_DATABASE="未找到"
        CURRENT_MONGODB_URI="未找到"
        CURRENT_RABBITMQ_ADDRESS="未找到"
        CURRENT_RABBITMQ_USERNAME="未找到"
        CURRENT_RABBITMQ_PASSWORD="未找到"
        CURRENT_NACOS_PORT="未找到"
        CURRENT_NACOS_USERNAME="未找到"
        CURRENT_NACOS_PASSWORD="未找到"
        CURRENT_NACOS_NAMESPACE="未找到"
        CURRENT_STORAGE_ENDPOINT="未找到"
        CURRENT_STORAGE_ACCESS_KEY="未找到"
        CURRENT_STORAGE_SECRET_KEY="未找到"
        CURRENT_STORAGE_BUCKET="未找到"
        CURRENT_BSP_BASE_URL="未找到"
        CURRENT_BPM_BASE_URL="未找到"
        CURRENT_XXL_ADDRESSES="未找到"
        CURRENT_XXL_ADMIN_USERNAME="未找到"
        CURRENT_XXL_ADMIN_PASSWORD="未找到"
        CURRENT_XXL_EXECUTOR_IP="未找到"
        CURRENT_XXL_EXECUTOR_PORT="未找到"
        CURRENT_QYSH_SERVER_IP="未找到"
        CURRENT_QYSH_WEBSOCKET_PORT="未找到"
        CURRENT_QYSH_WEB_PORT="未找到"
        CURRENT_QYSH_USERNAME="未找到"
        CURRENT_QYSH_PASSWORD="未找到"
        CURRENT_HAIKANG_REGION="未找到"
        CURRENT_HAIKANG_ORGCODE="未找到"
        CURRENT_HAIKANG_HOST="未找到"
        CURRENT_HAIKANG_APPKEY="未找到"
        CURRENT_HAIKANG_APPSECRET="未找到"
        return
    fi

    print_info "正在分析配置文件: $acp_config_file"

    # 安全提取配置值的函数
    safe_extract() {
        local value="$1"
        # 移除前后空格、制表符、换行符等
        echo "$value" | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//' | tr -d '\r\n'
    }

    # 提取各种配置的当前值
    # 提取服务器IP配置
    local server_ip=$(extract_config_value "$acp_config_file" "conf.server.ip" "")
    if [ ! -z "$server_ip" ] && [ "$server_ip" != "未找到" ]; then
        CURRENT_SERVER_IP=$(safe_extract "$server_ip")
    else
        CURRENT_SERVER_IP="未找到"
    fi



    # 系统配置
    CURRENT_IGNORES=$(safe_extract "$(awk '/matchers:/,/debug:/ {if(/ignores:/) print}' "$acp_config_file" | head -1 | sed 's/.*ignores: *//' | sed 's/ *$//')")

    # PostgreSQL配置
    CURRENT_PG_URL=$(safe_extract "$(grep -E "^\s*url:.*postgresql" "$acp_config_file" | head -1 | sed 's/.*url: *//' | sed 's/ *$//')")
    CURRENT_PG_USERNAME=$(safe_extract "$(awk '/postgresql/,/bsp:/ {if(/username:/ && !/nacos/) print}' "$acp_config_file" | head -1 | sed 's/.*username: *//' | sed 's/ *$//')")
    CURRENT_PG_PASSWORD=$(safe_extract "$(awk '/postgresql/,/bsp:/ {if(/password:/ && !/nacos/) print}' "$acp_config_file" | head -1 | sed 's/.*password: *//' | sed 's/ *$//')")

    # MySQL配置
    CURRENT_MYSQL_URL=$(safe_extract "$(grep -E "^\s*url:.*mysql" "$acp_config_file" | head -1 | sed 's/.*url: *//' | sed 's/ *$//')")
    CURRENT_MYSQL_USERNAME=$(safe_extract "$(awk '/mysql/,/mongodb:/ {if(/username:/) print}' "$acp_config_file" | head -1 | sed 's/.*username: *//' | sed 's/ *$//')")
    CURRENT_MYSQL_PASSWORD=$(safe_extract "$(awk '/mysql/,/mongodb:/ {if(/password:/) print}' "$acp_config_file" | head -1 | sed 's/.*password: *//' | sed 's/ *$//')")

    # Redis配置
    CURRENT_REDIS_HOST=$(safe_extract "$(awk '/redis:/,/rabbitmq:/ {if(/host:/) print}' "$acp_config_file" | head -1 | sed 's/.*host: *//' | sed 's/ *$//')")
    CURRENT_REDIS_PORT=$(safe_extract "$(awk '/redis:/,/rabbitmq:/ {if(/port:/) print}' "$acp_config_file" | head -1 | sed 's/.*port: *//' | sed 's/ *$//')")
    CURRENT_REDIS_PASSWORD=$(safe_extract "$(awk '/redis:/,/rabbitmq:/ {if(/password:/) print}' "$acp_config_file" | head -1 | sed 's/.*password: *//' | sed 's/ *$//')")
    CURRENT_REDIS_DATABASE=$(safe_extract "$(awk '/redis:/,/rabbitmq:/ {if(/database:/) print}' "$acp_config_file" | head -1 | sed 's/.*database: *//' | sed 's/ *$//')")

    # MongoDB配置
    CURRENT_MONGODB_URI=$(safe_extract "$(grep -E "^\s*uri:.*mongodb" "$acp_config_file" | head -1 | sed 's/.*uri: *//' | sed 's/ *$//')")

    # RabbitMQ配置
    CURRENT_RABBITMQ_ADDRESS=$(safe_extract "$(awk '/rabbitmq:/,/device:/ {if(/addresses:/) print}' "$acp_config_file" | head -1 | sed 's/.*addresses: *//' | sed 's/ *$//')")
    CURRENT_RABBITMQ_USERNAME=$(safe_extract "$(awk '/rabbitmq:/,/device:/ {if(/username:/) print}' "$acp_config_file" | head -1 | sed 's/.*username: *//' | sed 's/ *$//')")
    CURRENT_RABBITMQ_PASSWORD=$(safe_extract "$(awk '/rabbitmq:/,/device:/ {if(/password:/) print}' "$acp_config_file" | head -1 | sed 's/.*password: *//' | sed 's/ *$//')")

    # Nacos配置
    CURRENT_NACOS_PORT=$(safe_extract "$(awk '/nacos:/,/---/ {if(/port:/) print}' "$acp_config_file" | head -1 | sed 's/.*port: *//' | sed 's/ *$//')")
    CURRENT_NACOS_USERNAME=$(safe_extract "$(awk '/nacos:/,/---/ {if(/username:/) print}' "$acp_config_file" | head -1 | sed 's/.*username: *//' | sed 's/ *$//')")
    CURRENT_NACOS_PASSWORD=$(safe_extract "$(awk '/nacos:/,/---/ {if(/password:/) print}' "$acp_config_file" | head -1 | sed 's/.*password: *//' | sed 's/ *$//')")
    CURRENT_NACOS_NAMESPACE=$(safe_extract "$(awk '/nacos:/,/---/ {if(/namespace:/) print}' "$acp_config_file" | head -1 | sed 's/.*namespace: *//' | sed 's/ *$//')")

    # 文件存储配置
    CURRENT_STORAGE_ENDPOINT=$(safe_extract "$(awk '/x-file-storage:/,/---/ {if(/end-point:/) print}' "$acp_config_file" | head -1 | sed 's/.*end-point: *//' | sed 's/ *$//')")
    CURRENT_STORAGE_ACCESS_KEY=$(safe_extract "$(awk '/x-file-storage:/,/---/ {if(/access-key:/) print}' "$acp_config_file" | head -1 | sed 's/.*access-key: *//' | sed 's/ *$//')")
    CURRENT_STORAGE_SECRET_KEY=$(safe_extract "$(awk '/x-file-storage:/,/---/ {if(/secret-key:/) print}' "$acp_config_file" | head -1 | sed 's/.*secret-key: *//' | sed 's/ *$//')")
    CURRENT_STORAGE_BUCKET=$(safe_extract "$(awk '/x-file-storage:/,/---/ {if(/bucket-name:/) print}' "$acp_config_file" | head -1 | sed 's/.*bucket-name: *//' | sed 's/ *$//')")

    # BSP/BPM配置
    CURRENT_BSP_BASE_URL=$(safe_extract "$(awk '/bsp:/,/bpm:/ {if(/baseUrl:/) print}' "$acp_config_file" | head -1 | sed 's/.*baseUrl: *//' | sed 's/ *$//')")
    CURRENT_BPM_BASE_URL=$(safe_extract "$(awk '/bpm:/,/---/ {if(/baseUrl:/) print}' "$acp_config_file" | head -1 | sed 's/.*baseUrl: *//' | sed 's/ *$//')")

    # XXL-Job配置
    CURRENT_XXL_ADDRESSES=$(safe_extract "$(awk '/xxl:/,/---/ {if(/addresses:/) print}' "$acp_config_file" | head -1 | sed 's/.*addresses: *//' | sed 's/ *$//')")
    CURRENT_XXL_ADMIN_USERNAME=$(safe_extract "$(awk '/admin:/,/executor:/ {if(/username:/) print}' "$acp_config_file" | head -1 | sed 's/.*username: *//' | sed 's/ *$//')")
    CURRENT_XXL_ADMIN_PASSWORD=$(safe_extract "$(awk '/admin:/,/executor:/ {if(/password:/) print}' "$acp_config_file" | head -1 | sed 's/.*password: *//' | sed 's/ *$//')")
    CURRENT_XXL_EXECUTOR_IP=$(safe_extract "$(awk '/executor:/,/haikang:/ {if(/ip:/) print}' "$acp_config_file" | head -1 | sed 's/.*ip: *//' | sed 's/ *$//')")
    CURRENT_XXL_EXECUTOR_PORT=$(safe_extract "$(awk '/executor:/,/haikang:/ {if(/port:/) print}' "$acp_config_file" | head -1 | sed 's/.*port: *//' | sed 's/ *$//')")

    # 清研手环(qysh)配置
    CURRENT_QYSH_SERVER_IP=$(safe_extract "$(awk '/qysh:/,/---/ {if(/serverIp:/) print}' "$acp_config_file" | head -1 | sed 's/.*serverIp: *//' | sed 's/ *$//')")
    CURRENT_QYSH_WEBSOCKET_PORT=$(safe_extract "$(awk '/qysh:/,/---/ {if(/websocketPort:/) print}' "$acp_config_file" | head -1 | sed 's/.*websocketPort: *//' | sed 's/ *$//')")
    CURRENT_QYSH_WEB_PORT=$(safe_extract "$(awk '/qysh:/,/---/ {if(/webPort:/) print}' "$acp_config_file" | head -1 | sed 's/.*webPort: *//' | sed 's/ *$//')")
    CURRENT_QYSH_USERNAME=$(safe_extract "$(awk '/qysh:/,/---/ {if(/username:/) print}' "$acp_config_file" | head -1 | sed 's/.*username: *//' | sed 's/ *$//')")
    CURRENT_QYSH_PASSWORD=$(safe_extract "$(awk '/qysh:/,/---/ {if(/password:/) print}' "$acp_config_file" | head -1 | sed 's/.*password: *//' | sed 's/ *$//')")

    # 海康威视(haikang)配置
    CURRENT_HAIKANG_REGION=$(safe_extract "$(awk '/haikang:/,/ribbon:/ {if(/region:/) print}' "$acp_config_file" | head -1 | sed 's/.*region: *//' | sed 's/ *$//')")
    CURRENT_HAIKANG_ORGCODE=$(safe_extract "$(awk '/haikang:/,/ribbon:/ {if(/orgCode:/) print}' "$acp_config_file" | head -1 | sed 's/.*orgCode: *//' | sed 's/ *$//')")
    CURRENT_HAIKANG_HOST=$(safe_extract "$(awk '/config:/,/ribbon:/ {if(/host:/) print}' "$acp_config_file" | head -1 | sed 's/.*host: *//' | sed 's/ *$//')")
    CURRENT_HAIKANG_APPKEY=$(safe_extract "$(awk '/config:/,/ribbon:/ {if(/appKey:/) print}' "$acp_config_file" | head -1 | sed 's/.*appKey: *//' | sed 's/ *$//')")
    CURRENT_HAIKANG_APPSECRET=$(safe_extract "$(awk '/config:/,/ribbon:/ {if(/appSecret:/) print}' "$acp_config_file" | head -1 | sed 's/.*appSecret: *//' | sed 's/ *$//')")
}

# 清理显示值的函数
clean_display_value() {
    local value="$1"
    # 移除所有控制字符、换行符、回车符，并清理前后空格
    echo "$value" | tr -d '\r\n\t' | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//' | tr -d '\033\007\010'
}

# 获取用户输入的配置值
get_user_input() {
    echo "请输入需要更新的配置值（直接回车跳过该项）："
    echo ""

    # 服务器配置
    if is_module_selected "server"; then
        echo "=== 服务器配置 ==="
        read -p "服务器IP地址 [当前: $(clean_display_value "$CURRENT_SERVER_IP")]: " SERVER_IP
        echo ""
    fi

    # 系统配置
    if is_module_selected "system"; then
        echo "=== 系统配置 ==="
        read -p "忽略路径配置 [当前: $(clean_display_value "$CURRENT_IGNORES")]: " IGNORES
        echo ""
    fi

    # 数据库配置
    if is_module_selected "database"; then
        echo "=== 数据库配置 ==="
        read -p "PostgreSQL连接URL [当前: $(clean_display_value "$CURRENT_PG_URL")]: " PG_URL
        read -p "PostgreSQL用户名 [当前: $(clean_display_value "$CURRENT_PG_USERNAME")]: " PG_USERNAME
        read -p "PostgreSQL密码 [当前: $(clean_display_value "$CURRENT_PG_PASSWORD")]: " PG_PASSWORD
        echo ""

        read -p "MySQL连接URL [当前: $(clean_display_value "$CURRENT_MYSQL_URL")]: " MYSQL_URL
        read -p "MySQL用户名 [当前: $(clean_display_value "$CURRENT_MYSQL_USERNAME")]: " MYSQL_USERNAME
        read -p "MySQL密码 [当前: $(clean_display_value "$CURRENT_MYSQL_PASSWORD")]: " MYSQL_PASSWORD
        echo ""
    fi

    # 中间件配置
    if is_module_selected "middleware"; then
        echo "=== 中间件配置 ==="

        # Redis配置
        echo "--- Redis配置 ---"
        read -p "Redis服务器地址 [当前: $CURRENT_REDIS_HOST]: " REDIS_HOST
        read -p "Redis端口 [当前: $CURRENT_REDIS_PORT]: " REDIS_PORT
        read -p "Redis密码 [当前: $CURRENT_REDIS_PASSWORD]: " REDIS_PASSWORD
        read -p "Redis数据库编号 [当前: $CURRENT_REDIS_DATABASE]: " REDIS_DATABASE
        echo ""

        # MongoDB配置
        echo "--- MongoDB配置 ---"
        read -p "MongoDB连接URI [当前: $CURRENT_MONGODB_URI]: " MONGODB_URI
        echo ""

        # RabbitMQ配置
        echo "--- RabbitMQ配置 ---"
        read -p "RabbitMQ地址 [当前: $CURRENT_RABBITMQ_ADDRESS]: " RABBITMQ_ADDRESS
        read -p "RabbitMQ用户名 [当前: $CURRENT_RABBITMQ_USERNAME]: " RABBITMQ_USERNAME
        read -p "RabbitMQ密码 [当前: $CURRENT_RABBITMQ_PASSWORD]: " RABBITMQ_PASSWORD
        echo ""
    fi

    # Nacos配置
    if is_module_selected "nacos"; then
        echo "=== Nacos配置 ==="
        read -p "Nacos端口 [当前: $CURRENT_NACOS_PORT]: " NACOS_PORT
        read -p "Nacos用户名 [当前: $CURRENT_NACOS_USERNAME]: " NACOS_USERNAME
        read -p "Nacos密码 [当前: $CURRENT_NACOS_PASSWORD]: " NACOS_PASSWORD
        read -p "Nacos命名空间 [当前: $CURRENT_NACOS_NAMESPACE]: " NACOS_NAMESPACE
        echo ""
    fi

    # 文件存储配置
    if is_module_selected "storage"; then
        echo "=== 文件存储配置 ==="
        read -p "文件存储端点 [当前: $CURRENT_STORAGE_ENDPOINT]: " STORAGE_ENDPOINT
        read -p "文件存储访问密钥 [当前: $CURRENT_STORAGE_ACCESS_KEY]: " STORAGE_ACCESS_KEY
        read -p "文件存储秘密密钥 [当前: $CURRENT_STORAGE_SECRET_KEY]: " STORAGE_SECRET_KEY
        read -p "存储桶名称 [当前: $CURRENT_STORAGE_BUCKET]: " STORAGE_BUCKET
        echo ""
    fi

    # 业务服务配置
    if is_module_selected "business"; then
        echo "=== 业务服务配置 ==="
        read -p "BSP基础URL [当前: $CURRENT_BSP_BASE_URL]: " BSP_BASE_URL
        read -p "BPM基础URL [当前: $CURRENT_BPM_BASE_URL]: " BPM_BASE_URL
        echo ""
    fi

    # XXL-Job配置
    if is_module_selected "xxljob"; then
        echo "=== XXL-Job配置 ==="
        read -p "XXL-Job管理地址 [当前: $CURRENT_XXL_ADDRESSES]: " XXL_ADDRESSES
        read -p "XXL-Job管理员用户名 [当前: $CURRENT_XXL_ADMIN_USERNAME]: " XXL_ADMIN_USERNAME
        read -p "XXL-Job管理员密码 [当前: $CURRENT_XXL_ADMIN_PASSWORD]: " XXL_ADMIN_PASSWORD
        read -p "XXL-Job执行器IP [当前: $CURRENT_XXL_EXECUTOR_IP]: " XXL_EXECUTOR_IP
        read -p "XXL-Job执行器端口 [当前: $CURRENT_XXL_EXECUTOR_PORT]: " XXL_EXECUTOR_PORT
        echo ""
    fi

    # 清研手环配置
    if is_module_selected "qysh"; then
        echo "=== 清研手环(QYSH)配置 ==="
        read -p "清研手环服务器IP [当前: $CURRENT_QYSH_SERVER_IP]: " QYSH_SERVER_IP
        read -p "清研手环WebSocket端口 [当前: $CURRENT_QYSH_WEBSOCKET_PORT]: " QYSH_WEBSOCKET_PORT
        read -p "清研手环Web端口 [当前: $CURRENT_QYSH_WEB_PORT]: " QYSH_WEB_PORT
        read -p "清研手环用户名 [当前: $CURRENT_QYSH_USERNAME]: " QYSH_USERNAME
        read -p "清研手环密码 [当前: $CURRENT_QYSH_PASSWORD]: " QYSH_PASSWORD
        echo ""
    fi

    # 海康威视配置
    if is_module_selected "haikang"; then
        echo "=== 海康威视(Haikang)配置 ==="
        read -p "海康威视区域代码 [当前: $CURRENT_HAIKANG_REGION]: " HAIKANG_REGION
        read -p "海康威视组织代码 [当前: $CURRENT_HAIKANG_ORGCODE]: " HAIKANG_ORGCODE
        read -p "海康威视主机地址 [当前: $CURRENT_HAIKANG_HOST]: " HAIKANG_HOST
        read -p "海康威视应用密钥 [当前: $CURRENT_HAIKANG_APPKEY]: " HAIKANG_APPKEY
        read -p "海康威视应用秘钥 [当前: $CURRENT_HAIKANG_APPSECRET]: " HAIKANG_APPSECRET
        echo ""
    fi
}

# 安全转义特殊字符用于sed
escape_for_sed() {
    local input="$1"
    # 转义sed中的特殊字符
    echo "$input" | sed 's/[[\.*^$()+?{|]/\\&/g' | sed 's|/|\\/|g'
}

# 更新单个配置文件
update_config_file() {
    local file_path="$1"
    print_info "正在更新文件: $file_path"

    # 备份原文件
    backup_file "$file_path"

    # 统计更新和跳过的配置项
    local updated_count=0
    local skipped_count=0
    local missing_configs=()

    # 提取当前文件的配置值
    local current_server_ip=$(extract_config_value "$file_path" "conf.server.ip" "")
    local current_acp_port=$(extract_config_value "$file_path" "conf.server.port.acp" "")

    # 提取系统配置
    local current_ignores=$(awk '/matchers:/,/debug:/ {if(/ignores:/) print}' "$file_path" | head -1 | sed 's/.*ignores: *//' | sed 's/ *$//')

    # 提取数据库配置
    local current_pg_url=$(grep -E "^\s*url:.*postgresql" "$file_path" | head -1 | sed 's/.*url: *//' | sed 's/ *$//')
    local current_pg_username=$(awk '/postgresql/,/bsp:/ {if(/username:/ && !/nacos/) print}' "$file_path" | head -1 | sed 's/.*username: *//' | sed 's/ *$//')
    local current_pg_password=$(awk '/postgresql/,/bsp:/ {if(/password:/ && !/nacos/) print}' "$file_path" | head -1 | sed 's/.*password: *//' | sed 's/ *$//')

    local current_mysql_url=$(grep -E "^\s*url:.*mysql" "$file_path" | head -1 | sed 's/.*url: *//' | sed 's/ *$//')
    local current_mysql_username=$(awk '/mysql/,/mongodb:/ {if(/username:/) print}' "$file_path" | head -1 | sed 's/.*username: *//' | sed 's/ *$//')
    local current_mysql_password=$(awk '/mysql/,/mongodb:/ {if(/password:/) print}' "$file_path" | head -1 | sed 's/.*password: *//' | sed 's/ *$//')

    # 提取Redis配置
    local current_redis_host=$(awk '/redis:/,/rabbitmq:/ {if(/host:/) print}' "$file_path" | head -1 | sed 's/.*host: *//' | sed 's/ *$//')
    local current_redis_port=$(awk '/redis:/,/rabbitmq:/ {if(/port:/) print}' "$file_path" | head -1 | sed 's/.*port: *//' | sed 's/ *$//')
    local current_redis_password=$(awk '/redis:/,/rabbitmq:/ {if(/password:/) print}' "$file_path" | head -1 | sed 's/.*password: *//' | sed 's/ *$//')
    local current_redis_database=$(awk '/redis:/,/rabbitmq:/ {if(/database:/) print}' "$file_path" | head -1 | sed 's/.*database: *//' | sed 's/ *$//')

    # 提取其他配置
    local current_mongodb_uri=$(grep -E "^\s*uri:.*mongodb" "$file_path" | head -1 | sed 's/.*uri: *//' | sed 's/ *$//')
    local current_rabbitmq_address=$(awk '/rabbitmq:/,/device:/ {if(/addresses:/) print}' "$file_path" | head -1 | sed 's/.*addresses: *//' | sed 's/ *$//')
    local current_rabbitmq_username=$(awk '/rabbitmq:/,/device:/ {if(/username:/) print}' "$file_path" | head -1 | sed 's/.*username: *//' | sed 's/ *$//')
    local current_rabbitmq_password=$(awk '/rabbitmq:/,/device:/ {if(/password:/) print}' "$file_path" | head -1 | sed 's/.*password: *//' | sed 's/ *$//')

    # 提取XXL-Job配置
    local current_xxl_addresses=$(awk '/xxl:/,/---/ {if(/addresses:/) print}' "$file_path" | head -1 | sed 's/.*addresses: *//' | sed 's/ *$//')
    local current_xxl_admin_username=$(awk '/admin:/,/executor:/ {if(/username:/) print}' "$file_path" | head -1 | sed 's/.*username: *//' | sed 's/ *$//')
    local current_xxl_admin_password=$(awk '/admin:/,/executor:/ {if(/password:/) print}' "$file_path" | head -1 | sed 's/.*password: *//' | sed 's/ *$//')
    local current_xxl_executor_ip=$(awk '/executor:/,/haikang:/ {if(/ip:/) print}' "$file_path" | head -1 | sed 's/.*ip: *//' | sed 's/ *$//')
    local current_xxl_executor_port=$(awk '/executor:/,/haikang:/ {if(/port:/) print}' "$file_path" | head -1 | sed 's/.*port: *//' | sed 's/ *$//')

    # 提取清研手环(qysh)配置
    local current_qysh_server_ip=$(awk '/qysh:/,/---/ {if(/serverIp:/) print}' "$file_path" | head -1 | sed 's/.*serverIp: *//' | sed 's/ *$//')
    local current_qysh_websocket_port=$(awk '/qysh:/,/---/ {if(/websocketPort:/) print}' "$file_path" | head -1 | sed 's/.*websocketPort: *//' | sed 's/ *$//')
    local current_qysh_web_port=$(awk '/qysh:/,/---/ {if(/webPort:/) print}' "$file_path" | head -1 | sed 's/.*webPort: *//' | sed 's/ *$//')
    local current_qysh_username=$(awk '/qysh:/,/---/ {if(/username:/) print}' "$file_path" | head -1 | sed 's/.*username: *//' | sed 's/ *$//')
    local current_qysh_password=$(awk '/qysh:/,/---/ {if(/password:/) print}' "$file_path" | head -1 | sed 's/.*password: *//' | sed 's/ *$//')

    # 提取海康威视(haikang)配置
    local current_haikang_region=$(awk '/haikang:/,/ribbon:/ {if(/region:/) print}' "$file_path" | head -1 | sed 's/.*region: *//' | sed 's/ *$//')
    local current_haikang_orgcode=$(awk '/haikang:/,/ribbon:/ {if(/orgCode:/) print}' "$file_path" | head -1 | sed 's/.*orgCode: *//' | sed 's/ *$//')
    local current_haikang_host=$(awk '/config:/,/ribbon:/ {if(/host:/) print}' "$file_path" | head -1 | sed 's/.*host: *//' | sed 's/ *$//')
    local current_haikang_appkey=$(awk '/config:/,/ribbon:/ {if(/appKey:/) print}' "$file_path" | head -1 | sed 's/.*appKey: *//' | sed 's/ *$//')
    local current_haikang_appsecret=$(awk '/config:/,/ribbon:/ {if(/appSecret:/) print}' "$file_path" | head -1 | sed 's/.*appSecret: *//' | sed 's/ *$//')

    # 创建临时文件
    local temp_file=$(mktemp)
    cp "$file_path" "$temp_file"

    # 定义更新配置的辅助函数
    update_config_item() {
        local config_name="$1"
        local new_value="$2"
        local current_value="$3"
        local sed_command="$4"

        if [ ! -z "$new_value" ]; then
            if [ ! -z "$current_value" ]; then
                eval "$sed_command"
                print_success "  ✓ 已更新 $config_name: $current_value → $new_value"
                ((updated_count++))
            else
                print_warning "  ⚠ 跳过 $config_name: 在此文件中未找到该配置项"
                missing_configs+=("$config_name")
                ((skipped_count++))
            fi
        else
            if [ ! -z "$current_value" ]; then
                print_info "  - 保持 $config_name 不变: $current_value"
            fi
        fi
    }

    # 使用sed进行替换（只替换用户输入了新值的配置项）
    if [ ! -z "$SERVER_IP" ]; then
        if [ ! -z "$current_server_ip" ] && [ "$current_server_ip" != "未找到" ]; then
            local escaped_old=$(escape_for_sed "$current_server_ip")
            local escaped_new=$(escape_for_sed "$SERVER_IP")

            # 更新各种可能的IP配置位置
            local updated=false

            # 更新 conf.server.ip
            if sed -i "s/ip: $escaped_old/ip: $escaped_new/g" "$temp_file" 2>/dev/null; then
                updated=true
            fi

            # 更新 nacos.ip
            if sed -i "/nacos:/,/group:/ s/ip: $escaped_old/ip: $escaped_new/" "$temp_file" 2>/dev/null; then
                updated=true
            fi

            # 更新URL中的IP
            sed -i "s|//$escaped_old|//$escaped_new|g" "$temp_file" 2>/dev/null
            sed -i "s|http://$escaped_old|http://$escaped_new|g" "$temp_file" 2>/dev/null
            sed -i "s|mongodb://$escaped_old|mongodb://$escaped_new|g" "$temp_file" 2>/dev/null

            print_success "  ✓ 已更新服务器IP: $current_server_ip → $SERVER_IP"
            ((updated_count++))
        else
            print_warning "  ⚠ 跳过服务器IP: 在此文件中未找到该配置项"
            missing_configs+=("服务器IP")
            ((skipped_count++))
        fi
    else
        if [ ! -z "$current_server_ip" ] && [ "$current_server_ip" != "未找到" ]; then
            print_info "  - 保持服务器IP不变: $current_server_ip"
        fi
    fi



    # 系统配置更新
    if [ ! -z "$IGNORES" ]; then
        if [ ! -z "$current_ignores" ]; then
            local escaped_old_ignores=$(escape_for_sed "$current_ignores")
            local escaped_new_ignores=$(escape_for_sed "$IGNORES")
            sed -i "/matchers:/,/debug:/ s/ignores: $escaped_old_ignores/ignores: $escaped_new_ignores/" "$temp_file"
            print_success "  ✓ 已更新忽略路径配置: $current_ignores → $IGNORES"
            ((updated_count++))
        else
            print_warning "  ⚠ 跳过忽略路径配置: 在此文件中未找到该配置项"
            missing_configs+=("忽略路径配置")
            ((skipped_count++))
        fi
    else
        if [ ! -z "$current_ignores" ]; then
            print_info "  - 保持忽略路径配置不变: $current_ignores"
        fi
    fi

    # PostgreSQL配置更新
    if [ ! -z "$PG_URL" ] && [ ! -z "$current_pg_url" ]; then
        local escaped_old_url=$(escape_for_sed "$current_pg_url")
        local escaped_new_url=$(escape_for_sed "$PG_URL")
        sed -i "s|url: $escaped_old_url|url: $escaped_new_url|g" "$temp_file"
    fi
    if [ ! -z "$PG_USERNAME" ] && [ ! -z "$current_pg_username" ]; then
        sed -i "/driver-class-name: org\.postgresql\.Driver/,/password:/ s/username: $current_pg_username/username: $PG_USERNAME/" "$temp_file"
    fi
    if [ ! -z "$PG_PASSWORD" ] && [ ! -z "$current_pg_password" ]; then
        local escaped_old_pwd=$(escape_for_sed "$current_pg_password")
        local escaped_new_pwd=$(escape_for_sed "$PG_PASSWORD")
        sed -i "/driver-class-name: org\.postgresql\.Driver/,/bsp:/ s/password: $escaped_old_pwd/password: $escaped_new_pwd/" "$temp_file"
    fi

    # MySQL配置更新
    if [ ! -z "$MYSQL_URL" ] && [ ! -z "$current_mysql_url" ]; then
        local escaped_old_url=$(escape_for_sed "$current_mysql_url")
        local escaped_new_url=$(escape_for_sed "$MYSQL_URL")
        sed -i "s|url: $escaped_old_url|url: $escaped_new_url|g" "$temp_file"
    fi
    if [ ! -z "$MYSQL_USERNAME" ] && [ ! -z "$current_mysql_username" ]; then
        sed -i "/jdbc:mysql:/,/password:/ s/username: $current_mysql_username/username: $MYSQL_USERNAME/" "$temp_file"
    fi
    if [ ! -z "$MYSQL_PASSWORD" ] && [ ! -z "$current_mysql_password" ]; then
        local escaped_old_pwd=$(escape_for_sed "$current_mysql_password")
        local escaped_new_pwd=$(escape_for_sed "$MYSQL_PASSWORD")
        sed -i "/jdbc:mysql:/,/mongodb:/ s/password: $escaped_old_pwd/password: $escaped_new_pwd/" "$temp_file"
    fi

    # Redis配置更新
    if [ ! -z "$REDIS_HOST" ] && [ ! -z "$current_redis_host" ]; then
        sed -i "/redis:/,/rabbitmq:/ s/host: $current_redis_host/host: $REDIS_HOST/" "$temp_file"
    fi
    if [ ! -z "$REDIS_PORT" ] && [ ! -z "$current_redis_port" ]; then
        sed -i "/redis:/,/rabbitmq:/ s/port: $current_redis_port/port: $REDIS_PORT/" "$temp_file"
    fi
    if [ ! -z "$REDIS_PASSWORD" ] && [ ! -z "$current_redis_password" ]; then
        local escaped_old_pwd=$(escape_for_sed "$current_redis_password")
        local escaped_new_pwd=$(escape_for_sed "$REDIS_PASSWORD")
        sed -i "/redis:/,/rabbitmq:/ s/password: $escaped_old_pwd/password: $escaped_new_pwd/" "$temp_file"
    fi
    if [ ! -z "$REDIS_DATABASE" ] && [ ! -z "$current_redis_database" ]; then
        sed -i "/redis:/,/rabbitmq:/ s/database: $current_redis_database/database: $REDIS_DATABASE/" "$temp_file"
    fi

    # MongoDB配置更新
    if [ ! -z "$MONGODB_URI" ] && [ ! -z "$current_mongodb_uri" ]; then
        local escaped_old_uri=$(escape_for_sed "$current_mongodb_uri")
        local escaped_new_uri=$(escape_for_sed "$MONGODB_URI")
        sed -i "s|uri: $escaped_old_uri|uri: $escaped_new_uri|g" "$temp_file"
    fi

    # RabbitMQ配置更新
    if [ ! -z "$RABBITMQ_ADDRESS" ] && [ ! -z "$current_rabbitmq_address" ]; then
        sed -i "s/addresses: $current_rabbitmq_address/addresses: $RABBITMQ_ADDRESS/g" "$temp_file"
    fi
    if [ ! -z "$RABBITMQ_USERNAME" ] && [ ! -z "$current_rabbitmq_username" ]; then
        sed -i "/rabbitmq:/,/device:/ s/username: $current_rabbitmq_username/username: $RABBITMQ_USERNAME/" "$temp_file"
    fi
    if [ ! -z "$RABBITMQ_PASSWORD" ] && [ ! -z "$current_rabbitmq_password" ]; then
        local escaped_old_pwd=$(escape_for_sed "$current_rabbitmq_password")
        local escaped_new_pwd=$(escape_for_sed "$RABBITMQ_PASSWORD")
        sed -i "/rabbitmq:/,/device:/ s/password: $escaped_old_pwd/password: $escaped_new_pwd/" "$temp_file"
        print_success "  ✓ 已更新RabbitMQ密码: $current_rabbitmq_password → $RABBITMQ_PASSWORD"
        ((updated_count++))
    elif [ ! -z "$RABBITMQ_PASSWORD" ]; then
        print_warning "  ⚠ 跳过RabbitMQ密码: 在此文件中未找到该配置项"
        missing_configs+=("RabbitMQ密码")
        ((skipped_count++))
    fi

    # XXL-Job配置更新
    if [ ! -z "$XXL_ADDRESSES" ] && [ ! -z "$current_xxl_addresses" ]; then
        local escaped_old_addr=$(escape_for_sed "$current_xxl_addresses")
        local escaped_new_addr=$(escape_for_sed "$XXL_ADDRESSES")
        sed -i "s|addresses: $escaped_old_addr|addresses: $escaped_new_addr|g" "$temp_file"
        print_success "  ✓ 已更新XXL-Job管理地址: $current_xxl_addresses → $XXL_ADDRESSES"
        ((updated_count++))
    elif [ ! -z "$XXL_ADDRESSES" ]; then
        print_warning "  ⚠ 跳过XXL-Job管理地址: 在此文件中未找到该配置项"
        missing_configs+=("XXL-Job管理地址")
        ((skipped_count++))
    fi

    if [ ! -z "$XXL_ADMIN_USERNAME" ] && [ ! -z "$current_xxl_admin_username" ]; then
        sed -i "/admin:/,/executor:/ s/username: $current_xxl_admin_username/username: $XXL_ADMIN_USERNAME/" "$temp_file"
        print_success "  ✓ 已更新XXL-Job管理员用户名: $current_xxl_admin_username → $XXL_ADMIN_USERNAME"
        ((updated_count++))
    elif [ ! -z "$XXL_ADMIN_USERNAME" ]; then
        print_warning "  ⚠ 跳过XXL-Job管理员用户名: 在此文件中未找到该配置项"
        missing_configs+=("XXL-Job管理员用户名")
        ((skipped_count++))
    fi

    if [ ! -z "$XXL_ADMIN_PASSWORD" ] && [ ! -z "$current_xxl_admin_password" ]; then
        local escaped_old_pwd=$(escape_for_sed "$current_xxl_admin_password")
        local escaped_new_pwd=$(escape_for_sed "$XXL_ADMIN_PASSWORD")
        sed -i "/admin:/,/executor:/ s/password: $escaped_old_pwd/password: $escaped_new_pwd/" "$temp_file"
        print_success "  ✓ 已更新XXL-Job管理员密码: $current_xxl_admin_password → $XXL_ADMIN_PASSWORD"
        ((updated_count++))
    elif [ ! -z "$XXL_ADMIN_PASSWORD" ]; then
        print_warning "  ⚠ 跳过XXL-Job管理员密码: 在此文件中未找到该配置项"
        missing_configs+=("XXL-Job管理员密码")
        ((skipped_count++))
    fi

    if [ ! -z "$XXL_EXECUTOR_IP" ] && [ ! -z "$current_xxl_executor_ip" ]; then
        sed -i "/executor:/,/haikang:/ s/ip: $current_xxl_executor_ip/ip: $XXL_EXECUTOR_IP/" "$temp_file"
        print_success "  ✓ 已更新XXL-Job执行器IP: $current_xxl_executor_ip → $XXL_EXECUTOR_IP"
        ((updated_count++))
    elif [ ! -z "$XXL_EXECUTOR_IP" ]; then
        print_warning "  ⚠ 跳过XXL-Job执行器IP: 在此文件中未找到该配置项"
        missing_configs+=("XXL-Job执行器IP")
        ((skipped_count++))
    fi

    if [ ! -z "$XXL_EXECUTOR_PORT" ] && [ ! -z "$current_xxl_executor_port" ]; then
        sed -i "/executor:/,/haikang:/ s/port: $current_xxl_executor_port/port: $XXL_EXECUTOR_PORT/" "$temp_file"
        print_success "  ✓ 已更新XXL-Job执行器端口: $current_xxl_executor_port → $XXL_EXECUTOR_PORT"
        ((updated_count++))
    elif [ ! -z "$XXL_EXECUTOR_PORT" ]; then
        print_warning "  ⚠ 跳过XXL-Job执行器端口: 在此文件中未找到该配置项"
        missing_configs+=("XXL-Job执行器端口")
        ((skipped_count++))
    fi

    # 清研手环(qysh)配置更新
    if [ ! -z "$QYSH_SERVER_IP" ] && [ ! -z "$current_qysh_server_ip" ]; then
        sed -i "/qysh:/,/---/ s/serverIp: $current_qysh_server_ip/serverIp: $QYSH_SERVER_IP/" "$temp_file"
        print_success "  ✓ 已更新清研手环服务器IP: $current_qysh_server_ip → $QYSH_SERVER_IP"
        ((updated_count++))
    elif [ ! -z "$QYSH_SERVER_IP" ]; then
        print_warning "  ⚠ 跳过清研手环服务器IP: 在此文件中未找到该配置项"
        missing_configs+=("清研手环服务器IP")
        ((skipped_count++))
    fi

    if [ ! -z "$QYSH_WEBSOCKET_PORT" ] && [ ! -z "$current_qysh_websocket_port" ]; then
        sed -i "/qysh:/,/---/ s/websocketPort: $current_qysh_websocket_port/websocketPort: $QYSH_WEBSOCKET_PORT/" "$temp_file"
        print_success "  ✓ 已更新清研手环WebSocket端口: $current_qysh_websocket_port → $QYSH_WEBSOCKET_PORT"
        ((updated_count++))
    elif [ ! -z "$QYSH_WEBSOCKET_PORT" ]; then
        print_warning "  ⚠ 跳过清研手环WebSocket端口: 在此文件中未找到该配置项"
        missing_configs+=("清研手环WebSocket端口")
        ((skipped_count++))
    fi

    if [ ! -z "$QYSH_WEB_PORT" ] && [ ! -z "$current_qysh_web_port" ]; then
        sed -i "/qysh:/,/---/ s/webPort: $current_qysh_web_port/webPort: $QYSH_WEB_PORT/" "$temp_file"
        print_success "  ✓ 已更新清研手环Web端口: $current_qysh_web_port → $QYSH_WEB_PORT"
        ((updated_count++))
    elif [ ! -z "$QYSH_WEB_PORT" ]; then
        print_warning "  ⚠ 跳过清研手环Web端口: 在此文件中未找到该配置项"
        missing_configs+=("清研手环Web端口")
        ((skipped_count++))
    fi

    if [ ! -z "$QYSH_USERNAME" ] && [ ! -z "$current_qysh_username" ]; then
        sed -i "/qysh:/,/---/ s/username: $current_qysh_username/username: $QYSH_USERNAME/" "$temp_file"
        print_success "  ✓ 已更新清研手环用户名: $current_qysh_username → $QYSH_USERNAME"
        ((updated_count++))
    elif [ ! -z "$QYSH_USERNAME" ]; then
        print_warning "  ⚠ 跳过清研手环用户名: 在此文件中未找到该配置项"
        missing_configs+=("清研手环用户名")
        ((skipped_count++))
    fi

    if [ ! -z "$QYSH_PASSWORD" ] && [ ! -z "$current_qysh_password" ]; then
        local escaped_old_pwd=$(escape_for_sed "$current_qysh_password")
        local escaped_new_pwd=$(escape_for_sed "$QYSH_PASSWORD")
        sed -i "/qysh:/,/---/ s/password: $escaped_old_pwd/password: $escaped_new_pwd/" "$temp_file"
        print_success "  ✓ 已更新清研手环密码: $current_qysh_password → $QYSH_PASSWORD"
        ((updated_count++))
    elif [ ! -z "$QYSH_PASSWORD" ]; then
        print_warning "  ⚠ 跳过清研手环密码: 在此文件中未找到该配置项"
        missing_configs+=("清研手环密码")
        ((skipped_count++))
    fi

    # 海康威视(haikang)配置更新
    if [ ! -z "$HAIKANG_REGION" ] && [ ! -z "$current_haikang_region" ]; then
        sed -i "/haikang:/,/ribbon:/ s/region: $current_haikang_region/region: $HAIKANG_REGION/" "$temp_file"
        print_success "  ✓ 已更新海康威视区域代码: $current_haikang_region → $HAIKANG_REGION"
        ((updated_count++))
    elif [ ! -z "$HAIKANG_REGION" ]; then
        print_warning "  ⚠ 跳过海康威视区域代码: 在此文件中未找到该配置项"
        missing_configs+=("海康威视区域代码")
        ((skipped_count++))
    fi

    if [ ! -z "$HAIKANG_ORGCODE" ] && [ ! -z "$current_haikang_orgcode" ]; then
        sed -i "/haikang:/,/ribbon:/ s/orgCode: $current_haikang_orgcode/orgCode: $HAIKANG_ORGCODE/" "$temp_file"
        print_success "  ✓ 已更新海康威视组织代码: $current_haikang_orgcode → $HAIKANG_ORGCODE"
        ((updated_count++))
    elif [ ! -z "$HAIKANG_ORGCODE" ]; then
        print_warning "  ⚠ 跳过海康威视组织代码: 在此文件中未找到该配置项"
        missing_configs+=("海康威视组织代码")
        ((skipped_count++))
    fi

    if [ ! -z "$HAIKANG_HOST" ] && [ ! -z "$current_haikang_host" ]; then
        sed -i "/config:/,/ribbon:/ s/host: $current_haikang_host/host: $HAIKANG_HOST/" "$temp_file"
        print_success "  ✓ 已更新海康威视主机地址: $current_haikang_host → $HAIKANG_HOST"
        ((updated_count++))
    elif [ ! -z "$HAIKANG_HOST" ]; then
        print_warning "  ⚠ 跳过海康威视主机地址: 在此文件中未找到该配置项"
        missing_configs+=("海康威视主机地址")
        ((skipped_count++))
    fi

    if [ ! -z "$HAIKANG_APPKEY" ] && [ ! -z "$current_haikang_appkey" ]; then
        local escaped_old_key=$(escape_for_sed "$current_haikang_appkey")
        local escaped_new_key=$(escape_for_sed "$HAIKANG_APPKEY")
        sed -i "/config:/,/ribbon:/ s/appKey: $escaped_old_key/appKey: $escaped_new_key/" "$temp_file"
        print_success "  ✓ 已更新海康威视应用密钥: $current_haikang_appkey → $HAIKANG_APPKEY"
        ((updated_count++))
    elif [ ! -z "$HAIKANG_APPKEY" ]; then
        print_warning "  ⚠ 跳过海康威视应用密钥: 在此文件中未找到该配置项"
        missing_configs+=("海康威视应用密钥")
        ((skipped_count++))
    fi

    if [ ! -z "$HAIKANG_APPSECRET" ] && [ ! -z "$current_haikang_appsecret" ]; then
        local escaped_old_secret=$(escape_for_sed "$current_haikang_appsecret")
        local escaped_new_secret=$(escape_for_sed "$HAIKANG_APPSECRET")
        sed -i "/config:/,/ribbon:/ s/appSecret: $escaped_old_secret/appSecret: $escaped_new_secret/" "$temp_file"
        print_success "  ✓ 已更新海康威视应用秘钥: $current_haikang_appsecret → $HAIKANG_APPSECRET"
        ((updated_count++))
    elif [ ! -z "$HAIKANG_APPSECRET" ]; then
        print_warning "  ⚠ 跳过海康威视应用秘钥: 在此文件中未找到该配置项"
        missing_configs+=("海康威视应用秘钥")
        ((skipped_count++))
    fi

    # 将更新后的内容写回原文件
    mv "$temp_file" "$file_path"

    # 显示更新统计
    echo ""
    if [ $updated_count -gt 0 ]; then
        print_success "文件更新完成: $file_path"
        print_info "  - 成功更新: $updated_count 个配置项"
    fi

    if [ $skipped_count -gt 0 ]; then
        print_warning "  - 跳过配置: $skipped_count 个配置项"
        if [ ${#missing_configs[@]} -gt 0 ]; then
            print_info "  - 缺失配置项: ${missing_configs[*]}"
        fi
    fi

    if [ $updated_count -eq 0 ] && [ $skipped_count -eq 0 ]; then
        print_info "文件无需更新: $file_path"
        print_info "  - 所有配置项均保持原值不变"
    fi
}

# 主函数
main() {
    show_usage

    # 获取用户选择的配置模块
    get_module_selection

    # 查找配置文件
    find_and_display_config_files

    # 分析rs-acp配置文件以获取当前值
    analyze_first_config

    # 获取用户输入
    get_user_input

    # 确认更新
    echo ""
    echo "=========================================="
    print_warning "即将更新 ${#CONFIG_FILES[@]} 个配置文件"
    echo "文件列表:"
    for file in "${CONFIG_FILES[@]}"; do
        echo "  - $file"
    done
    echo ""
    read -p "确认继续更新？(y/N): " confirm

    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        print_info "操作已取消"
        exit 0
    fi

    # 执行更新
    echo ""
    print_info "开始批量更新配置文件..."

    for file in "${CONFIG_FILES[@]}"; do
        update_config_file "$file"
    done

    echo ""
    print_success "所有配置文件更新完成！"
    print_info "备份文件已保存，如需恢复可使用备份文件"

    # 显示更新摘要
    echo ""
    echo "=========================================="
    echo "更新摘要:"
    echo "- 更新文件数量: ${#CONFIG_FILES[@]}"
    echo "- 备份文件位置: 原文件同目录下的 .backup.* 文件"
    echo "=========================================="
}

# 检查是否有必要的命令
check_dependencies() {
    local missing_deps=()

    for cmd in sed find; do
        if ! command -v "$cmd" &> /dev/null; then
            missing_deps+=("$cmd")
        fi
    done

    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_error "缺少必要的命令: ${missing_deps[*]}"
        print_error "请安装这些命令后重新运行脚本"
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    check_dependencies
    main "$@"
fi
