# 菜单选择功能演示

## 功能概述

脚本现在提供了友好的中文菜单界面，让用户可以直观地选择需要配置的模块，无需记忆复杂的命令行参数。

## 启动界面

当您运行脚本时，会看到以下界面：

```
==========================================
  application-conf.yml 批量更新工具
==========================================

此脚本将帮助您批量更新项目中所有的application-conf.yml配置文件

功能特性：
• 🎯 模块化配置 - 可选择性配置特定模块
• 🔄 智能默认值 - 直接回车保持原值不变
• 🛡️ 自动备份 - 更新前自动备份原文件
• 📊 详细反馈 - 清晰显示更新状态和统计
• 🔒 安全可靠 - 只修改用户指定的配置项

==========================================
  请选择要配置的模块
==========================================

0. 全部配置 (配置所有模块)
1. 服务器配置 (server) - IP地址
2. 系统配置 (system) - 忽略路径
3. 数据库配置 (database) - PostgreSQL、MySQL
4. 中间件配置 (middleware) - Redis、MongoDB、RabbitMQ
5. Nacos配置 (nacos) - 服务注册与发现
6. 文件存储配置 (storage) - 对象存储
7. 业务服务配置 (business) - BSP、BPM
8. XXL-Job配置 (xxljob) - 任务调度
9. 清研手环配置 (qysh) - 设备集成
10. 海康威视配置 (haikang) - 视频监控

请输入要配置的模块编号 (多个编号用空格分隔，如: 1 3 4): 
```

## 使用场景演示

### 场景1: 配置所有模块

**用户输入**: `0` 或直接回车

**系统响应**:
```
[INFO] 已选择: 全部配置

[INFO] 正在搜索application-conf.yml文件...
  1. ./rs-module-acp/rs-module-acp-biz/src/main/resources/application-conf.yml
  2. ./rs-module-ihc/rs-module-ihc-biz/src/main/resources/application-conf.yml

[SUCCESS] 找到 2 个配置文件

请输入需要更新的配置值（直接回车跳过该项）：

=== 服务器配置 ===
服务器IP地址 [当前: *************]: 
ACP服务端口 [当前: 9100]: 

=== 系统配置 ===
忽略路径配置 [当前: /doc.html/**,/acp/app/msg/js/**]: 

=== 数据库配置 ===
...
```

### 场景2: 只配置数据库

**用户输入**: `3`

**系统响应**:
```
[INFO] 已选择: 数据库配置

[INFO] 正在搜索application-conf.yml文件...
[SUCCESS] 找到 2 个配置文件

请输入需要更新的配置值（直接回车跳过该项）：

=== 数据库配置 ===
PostgreSQL连接URL [当前: ******************************************]: *****************************************
PostgreSQL用户名 [当前: postgres]: 
PostgreSQL密码 [当前: Go@123456]: NewPassword123

MySQL连接URL [当前: ****************************************]: 
MySQL用户名 [当前: root]: 
MySQL密码 [当前: sundun_bsp]: 

确认继续更新？(y/N): y
```

### 场景3: 配置多个模块

**用户输入**: `1 3 4` (服务器、数据库、中间件)

**系统响应**:
```
[INFO] 已选择: 服务器配置 数据库配置 中间件配置

[INFO] 正在搜索application-conf.yml文件...
[SUCCESS] 找到 2 个配置文件

请输入需要更新的配置值（直接回车跳过该项）：

=== 服务器配置 ===
服务器IP地址 [当前: *************]: **********
ACP服务端口 [当前: 9100]: 

=== 数据库配置 ===
PostgreSQL连接URL [当前: ******************************************]: 
PostgreSQL用户名 [当前: postgres]: 
PostgreSQL密码 [当前: Go@123456]: 

MySQL连接URL [当前: ****************************************]: 
MySQL用户名 [当前: root]: 
MySQL密码 [当前: sundun_bsp]: 

=== 中间件配置 ===
--- Redis配置 ---
Redis服务器地址 [当前: *************]: **********
Redis端口 [当前: 6399]: 6379
Redis密码 [当前: redisbsp]: 
Redis数据库编号 [当前: 3]: 

--- MongoDB配置 ---
MongoDB连接URI [当前: mongodb://*************:27111/bsp]: 

--- RabbitMQ配置 ---
RabbitMQ地址 [当前: *************:5682]: 
RabbitMQ用户名 [当前: root]: 
RabbitMQ密码 [当前: sundun_bsp]: 
```

### 场景4: 设备配置

**用户输入**: `9 10` (清研手环、海康威视)

**系统响应**:
```
[INFO] 已选择: 清研手环配置 海康威视配置

请输入需要更新的配置值（直接回车跳过该项）：

=== 清研手环(QYSH)配置 ===
清研手环服务器IP [当前: *************]: **********
清研手环WebSocket端口 [当前: 48300]: 
清研手环Web端口 [当前: 8180]: 
清研手环用户名 [当前: admin]: 
清研手环密码 [当前: #LocalSense]: NewPassword

=== 海康威视(Haikang)配置 ===
海康威视区域代码 [当前: root00000000]: 
海康威视组织代码 [当前: 110000113]: 
海康威视主机地址 [当前: 127.0.0.1]: **********
海康威视应用密钥 [当前: xxxx]: real_app_key
海康威视应用秘钥 [当前: xxxx]: real_app_secret
```

### 场景5: 错误输入处理

**用户输入**: `15 abc`

**系统响应**:
```
[ERROR] 无效的选择: 15

[WARNING] 请输入有效的模块编号！

按回车键继续...

==========================================
  请选择要配置的模块
==========================================

0. 全部配置 (配置所有模块)
1. 服务器配置 (server) - IP地址、端口
...
```

## 优势特性

### 1. 🎯 直观易用
- 中文界面，无需记忆英文参数
- 清晰的模块分类和说明
- 支持多选和单选

### 2. 🚀 灵活选择
- 可以选择单个模块
- 可以选择多个模块组合
- 默认选择全部配置

### 3. 🔒 错误防护
- 输入验证和错误提示
- 重新选择机会
- 清晰的确认流程

### 4. 📊 清晰反馈
- 显示已选择的模块
- 中文模块名称显示
- 详细的操作指导

## 常用选择组合

### 环境迁移
```
选择: 1 (服务器配置)
用途: 快速更新IP地址和端口
```

### 数据库迁移
```
选择: 3 (数据库配置)
用途: 更新数据库连接信息
```

### 基础设施配置
```
选择: 1 3 4 5 (服务器+数据库+中间件+Nacos)
用途: 完整的基础设施配置更新
```

### 设备集成
```
选择: 9 10 (清研手环+海康威视)
用途: 配置外部设备集成
```

### 业务服务配置
```
选择: 6 7 8 (存储+业务+任务调度)
用途: 业务相关服务配置
```

## 使用建议

1. **首次使用**: 建议选择单个模块熟悉流程
2. **生产环境**: 谨慎选择，只更新必要的模块
3. **测试验证**: 更新后验证相关功能是否正常
4. **备份恢复**: 利用自动备份功能，必要时可快速恢复

这种菜单式的设计让配置管理变得更加直观和用户友好！
